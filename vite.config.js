import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    port: 8080,
    host: true,
    allowedHosts: [
      '8fe8-111-192-97-154.ngrok-free.app',
      '.ngrok-free.app'
    ],
    proxy: {
      // 将 /api 请求代理到 Express 服务器
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true
      },
      // 将 WebSocket 请求代理到 Express 服务器
      '/socket.io': {
        target: 'ws://localhost:3000',
        ws: true
      }
    }
  }
})