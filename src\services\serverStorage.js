// serverStorage.js - 提供与服务器通信的存储服务
import { cacheService } from './cacheService';

// 服务器API地址 - 自动检测当前环境
const getApiBaseUrl = () => {
  // 获取当前主机名和协议
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  const port = window.location.port;

  console.log(`Current hostname: ${hostname}, protocol: ${protocol}, port: ${port}`);

  // 如果是在开发环境中（localhost）
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    console.log('Using localhost API URL');
    return 'http://localhost:3000/api';
  }

  // 对于所有其他情况（包括内网穿透、IP访问等），都使用相对路径
  // 这样可以确保在任何域名下都能正确访问API
  console.log('Using relative API URL for universal access');
  return '/api';
};

const API_BASE_URL = getApiBaseUrl();

// 获取所有数据
async function getAllData() {
  try {
    const response = await fetch(`${API_BASE_URL}/data`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch all data:', error);
    return {};
  }
}

// 获取特定键的数据
async function getData(key) {
  // 生成缓存键
  const cacheKey = cacheService.generateCacheKey(`getData_${key}`);

  // 尝试从缓存获取
  const cachedData = cacheService.getCache(cacheKey);
  if (cachedData !== null) {
    console.log(`[ServerStorage] 从缓存返回数据: ${key}`);
    return cachedData;
  }

  const fullUrl = `${API_BASE_URL}/data/${key}`;

  console.log(`[ServerStorage] Attempting to fetch data for key: ${key}`);
  console.log(`[ServerStorage] API_BASE_URL: ${API_BASE_URL}`);
  console.log(`[ServerStorage] Full URL: ${fullUrl}`);

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时

    const response = await fetch(fullUrl, {
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    console.log(`[ServerStorage] Response status: ${response.status}`);
    console.log(`[ServerStorage] Response ok: ${response.ok}`);

    if (!response.ok) {
      if (response.status === 404) {
        console.log(`[ServerStorage] Key ${key} not found (404)`);
        // 缓存 null 结果，避免重复请求不存在的数据
        cacheService.setCache(cacheKey, null);
        return null; // 键不存在
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log(`[ServerStorage] Server response for ${key}:`, data);
    console.log(`[ServerStorage] Returning data[${key}]:`, data[key]);

    // 缓存成功获取的数据
    const result = data[key];
    cacheService.setCache(cacheKey, result);

    return result;
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error(`[ServerStorage] Request timeout for key ${key}`);
    } else {
      console.error(`[ServerStorage] Failed to fetch data for key ${key}:`, error);
      console.error(`[ServerStorage] Error details:`, {
        message: error.message,
        stack: error.stack,
        url: fullUrl
      });
    }
    return null;
  }
}

// 设置数据
async function setData(key, value) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时

    const response = await fetch(`${API_BASE_URL}/data/${key}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ value }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // 数据更新成功后，清除相关缓存并更新缓存
    if (result.success) {
      const cacheKey = cacheService.generateCacheKey(`getData_${key}`);
      cacheService.setCache(cacheKey, value); // 更新缓存为新值
      console.log(`[ServerStorage] 数据保存成功，已更新缓存: ${key}`);
    }

    return result;
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error(`Request timeout for saving key ${key}`);
      return { success: false, error: '请求超时，请检查服务器连接' };
    } else {
      console.error(`Failed to save data for key ${key}:`, error);
      return { success: false, error: error.message };
    }
  }
}

// 备份数据到本地文件
async function backupData() {
  try {
    // 获取所有数据
    const allData = await getAllData();

    if (!allData || Object.keys(allData).length === 0) {
      throw new Error('没有数据可以备份');
    }

    // 创建备份文件名（包含时间戳）
    const now = new Date();
    const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const filename = `store-data-backup-${timestamp}.json`;

    // 格式化数据为可读的JSON
    const jsonData = JSON.stringify(allData, null, 2);

    // 创建Blob对象
    const blob = new Blob([jsonData], { type: 'application/json' });

    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    URL.revokeObjectURL(url);

    console.log(`数据备份成功: ${filename}`);
    return { success: true, filename, message: '数据备份成功' };
  } catch (error) {
    console.error('备份数据失败:', error);
    return { success: false, error: error.message };
  }
}

// 导出函数
export const useServerStorage = () => {
  return {
    getAllData,
    getData,
    setData,
    backupData,
    // 缓存相关功能
    getCacheStats: cacheService.getCacheStats,
    clearCache: cacheService.clearCache
  };
};

// 兼容层 - 提供与localStorage相同的API
export const serverStorage = {
  getItem: async (key) => {
    const value = await getData(key);
    return value;
  },

  setItem: async (key, value) => {
    await setData(key, value);
  },

  // 注意：这些方法是异步的，与localStorage不同
  // 在迁移过程中需要注意处理Promise
};
