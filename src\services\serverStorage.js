// serverStorage.js - 提供与服务器通信的存储服务

// 服务器API地址 - 自动检测当前环境
const getApiBaseUrl = () => {
  // 获取当前主机名和协议
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;

  // 如果是在开发环境中
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'http://localhost:3000/api';
  }

  // 如果是通过ngrok访问（检测ngrok域名）
  if (hostname.includes('ngrok')) {
    // 使用相对路径，这样请求会发送到当前域名的/api路径
    return '/api';
  }

  // 如果是通过IP访问（例如从iPad访问）
  return `${protocol}//${hostname}:3000/api`;
};

const API_BASE_URL = getApiBaseUrl();

// 获取所有数据
async function getAllData() {
  try {
    const response = await fetch(`${API_BASE_URL}/data`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch all data:', error);
    return {};
  }
}

// 获取特定键的数据
async function getData(key) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时

    const response = await fetch(`${API_BASE_URL}/data/${key}`, {
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      if (response.status === 404) {
        return null; // 键不存在
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data[key];
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error(`Request timeout for key ${key}`);
    } else {
      console.error(`Failed to fetch data for key ${key}:`, error);
    }
    return null;
  }
}

// 设置数据
async function setData(key, value) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时

    const response = await fetch(`${API_BASE_URL}/data/${key}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ value }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error(`Request timeout for saving key ${key}`);
      return { success: false, error: '请求超时，请检查服务器连接' };
    } else {
      console.error(`Failed to save data for key ${key}:`, error);
      return { success: false, error: error.message };
    }
  }
}

// 导出函数
export const useServerStorage = () => {
  return {
    getAllData,
    getData,
    setData
  };
};

// 兼容层 - 提供与localStorage相同的API
export const serverStorage = {
  getItem: async (key) => {
    const value = await getData(key);
    return value;
  },

  setItem: async (key, value) => {
    await setData(key, value);
  },

  // 注意：这些方法是异步的，与localStorage不同
  // 在迁移过程中需要注意处理Promise
};
