<template>
  <div> <!-- 保留一个根元素 -->
    <!-- 导出和重置按钮区域 -->
    <div class="action-buttons">
        <button @click="exportTableToImage('product-table', '商品列表')">导出图片</button>
        <button @click="exportProductsToExcel">导出 Excel</button>
        <button class="reset-button" @click="handleReset">重置列表</button>
    </div>

    <!-- 先显示商品列表 -->
    <h2>商品列表</h2>
    <product-list
      id="product-table-component"
      :products="products"
      @sell-product="handleSell"
      @edit-product="openEditModal"
      @delete-product="handleDelete"
      @restock-product="handleRestock"
    />

    <hr />

    <!-- 再显示添加表单 -->
    <add-product-form @product-added="handleFeedback" />

    <hr />

    <!-- 最后显示批量导入 -->
    <bulk-import @import-complete="handleFeedback" />

    <!-- 编辑弹窗 -->
    <edit-product-modal
        v-if="editingProduct"
        :product="editingProduct"
        @close="closeEditModal"
        @save="handleSaveEdit"
        @delete="handleDeleteFromModal"
    />

    <!-- 反馈消息 -->
    <div v-if="feedbackMessage" class="feedback" :class="feedbackType">
      {{ feedbackMessage }}
    </div>

  </div>
</template>

<script setup>
import { ref } from 'vue';
import html2canvas from 'html2canvas'; // 导入 html2canvas
import * as XLSX from 'xlsx'; // 导入 xlsx
// 路径需要更新
import { useProducts } from '../services/productService.js';
import AddProductForm from '../components/AddProductForm.vue';
import ProductList from '../components/ProductList.vue';
import BulkImport from '../components/BulkImport.vue';
import EditProductModal from '../components/EditProductModal.vue';

const { products, addProduct, updateProduct, deleteProduct, sellProduct, restockProduct, resetProducts } = useProducts();

const editingProduct = ref(null);
const feedbackMessage = ref('');
const feedbackType = ref('info'); // 'info', 'success', 'error'
let feedbackTimeout = null;

// --- 反馈处理 --- //
function showFeedback(message, type = 'info', duration = 3000) {
  feedbackMessage.value = message;
  feedbackType.value = type;
  clearTimeout(feedbackTimeout);
  if (duration > 0) {
    feedbackTimeout = setTimeout(() => {
      feedbackMessage.value = '';
    }, duration);
  }
}

function handleFeedback(result) {
  if (result && result.message) {
    showFeedback(result.message, result.success ? 'success' : 'error');
  }
}

// --- 商品操作处理 --- //
function handleSell(productId) {
  const result = sellProduct(productId);
  // 检查是否成功并且是售罄消息
  if (result.success && result.message && result.message.includes('卖完了！需要尽快补货！！')) {
    alert(result.message); // 使用 alert 弹窗提示
  } else {
    // 对于其他成功或失败的情况，使用底部反馈栏
    handleFeedback(result);
  }
}

// --- 新增：处理补货 --- //
function handleRestock(productId) {
    console.log(`[ProductManagement] handleRestock called with ID: ${productId}`);
    const result = restockProduct(productId);
    // 可以只在实际发生补货时显示消息 (noAction 标志)
    if (result.success && !result.noAction) {
         showFeedback(result.message, 'success');
    } else if (!result.success) {
        showFeedback(result.message, 'error');
    } else if (result.noAction) {
        showFeedback(result.message, 'info'); // 库存已满的提示
    }
}
// --- 结束：处理补货 --- //

function handleDelete(productId) {
    if (confirm("确定要删除这个商品吗？")) {
        const result = deleteProduct(productId);
        handleFeedback(result);
    }
}

// --- 编辑弹窗处理 --- //
function openEditModal(product) {
  editingProduct.value = { ...product };
}

function closeEditModal() {
  editingProduct.value = null;
}

function handleSaveEdit(updatedProductData) {
    if (!editingProduct.value) return;
    const result = updateProduct(editingProduct.value.id, updatedProductData);
    handleFeedback(result);
    if (result.success) {
        closeEditModal();
    }
}

function handleDeleteFromModal(productId) {
    if (!editingProduct.value || editingProduct.value.id !== productId) return;
    if (confirm(`确定要从编辑窗口删除商品 '${editingProduct.value.name}' 吗？`)) {
        const result = deleteProduct(productId);
        handleFeedback(result);
        closeEditModal();
    }
}

// --- 修改：处理重置（完全清空商品列表） --- //
function handleReset() {
    if (confirm("确定要重置商品列表吗？此操作将清空所有商品，但不会影响已登记的账单记录。")) {
        const productResult = resetProducts();

        // 显示反馈
        if (productResult.success) {
            // 使用 resetProducts 函数返回的消息
            handleFeedback({
                success: true,
                message: productResult.message || "商品列表已重置！所有商品名称已缓存，账单记录中的商品名称将保持不变。"
            });
        } else {
            handleFeedback({ success: false, message: "重置商品列表过程中发生错误。" });
            console.error("Product Reset Result:", productResult);
        }
    }
}
// --- 结束：修改处理重置 --- //

// --- 导出功能 --- //

// 通用的导出表格到图片的函数
async function exportTableToImage(elementId, filename = 'export') {
    // 注意：html2canvas 可能无法直接捕获子组件内部的 table
    // 我们需要找到 ProductList 组件内部实际的 table 元素
    const listComponent = document.getElementById(elementId + '-component'); // 获取 ProductList 组件的根元素
    if (!listComponent) {
        alert('无法找到要导出的表格组件。');
        return;
    }
    const tableElement = listComponent.querySelector('table'); // 在组件内部查找 table
    if (!tableElement) {
        alert('无法找到要导出的表格。');
        return;
    }

    try {
        const canvas = await html2canvas(tableElement, {
            useCORS: true, // 如果表格中包含跨域图片，需要设置
            // scale: window.devicePixelRatio, // 可以提高清晰度，但可能增加文件大小和处理时间
        });
        const image = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.href = image;
        link.download = `${filename}.png`;
        link.click();
    } catch (error) {
        console.error('导出图片失败:', error);
        alert('导出图片失败，请查看控制台获取更多信息。');
    }
}

// 导出商品数据到 Excel
function exportProductsToExcel() {
    if (products.value.length === 0) {
        alert('没有商品数据可以导出。');
        return;
    }

    // 准备数据 (仅包含需要的列)
    const dataToExport = products.value.map(p => ({
        '物品名称': p.name,
        '价格': p.price,
        '在售数量': p.available,
        '剩余数量': p.remaining
    }));

    // 创建 worksheet
    const ws = XLSX.utils.json_to_sheet(dataToExport);

    // 创建 workbook
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '商品列表'); // sheet 名称

    // 导出文件
    XLSX.writeFile(wb, '商品列表.xlsx');
}

// --- 结束导出功能 --- //

</script>

<style scoped>
/* 这些样式现在特定于此视图 */
h1, h2 {
  color: #333;
}

.feedback {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid transparent;
}
.feedback.info {
  color: #004085;
  background-color: #cce5ff;
  border-color: #b8daff;
}
.feedback.success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}
.feedback.error {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

/* 全局模态框样式现在可以在 App.vue 或一个单独的 CSS 文件中定义 */
/* :global(...) 部分应该移出或调整 */
/* 暂时保留，但最好移到 App.vue 或全局 CSS */
:global(.modal-overlay) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

:global(.modal-content) {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
  min-width: 300px;
  max-width: 500px;
}

:global(.modal-close-button) {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #aaa;
}
:global(.modal-close-button:hover) {
    color: #333;
}

/* 将导出按钮区域重命名为通用操作按钮区域 */
.action-buttons {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    gap: 10px;
    flex-wrap: wrap; /* 允许换行 */
}

.action-buttons button {
    padding: 8px 15px;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* 导出按钮样式 */
.action-buttons button:not(.reset-button) {
     background-color: #007bff;
}
.action-buttons button:not(.reset-button):hover {
     background-color: #0056b3;
}

/* 重置按钮特定样式 */
.reset-button {
    background-color: #dc3545 !important; /* 使用 !important 覆盖通用按钮样式 */
}
.reset-button:hover {
    background-color: #c82333 !important;
}

/* 响应式设计 */
@media (min-width: 768px) {
  div {
    padding: 15px;
  }

  .action-buttons {
    justify-content: flex-start;
  }
}

@media (min-width: 1200px) {
  div {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }
}

/* 移动设备样式 */
@media (max-width: 767px) {
  div {
    padding: 10px;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .action-buttons button {
    width: 100%;
    margin-bottom: 5px;
  }

  h2 {
    font-size: 1.5em;
    text-align: center;
  }
}

</style>