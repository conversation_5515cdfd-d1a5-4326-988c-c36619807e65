// 应用配置文件
// 这里可以控制各种自动刷新和同步功能的开关

export const APP_CONFIG = {
  // 数据同步配置
  dataSync: {
    // 是否启用页面可见性变化时的自动刷新
    enableVisibilityRefresh: false,
    
    // 是否启用定时轮询
    enablePolling: false,
    
    // 是否启用实时数据同步
    enableRealtimeSync: false,
    
    // 轮询间隔（毫秒）
    pollingInterval: 300000, // 5分钟
    
    // 页面隐藏多久后触发刷新（毫秒）
    visibilityRefreshThreshold: 60000, // 1分钟
  },
  
  // 缓存配置
  cache: {
    // 是否启用内存缓存
    enableMemoryCache: true,
    
    // 是否启用持久化缓存
    enablePersistentCache: true,
    
    // 缓存过期时间（毫秒）
    cacheExpiration: 300000, // 5分钟
  },
  
  // 用户界面配置
  ui: {
    // 是否显示性能监控
    showPerformanceMonitor: false,
    
    // 是否显示调试信息
    showDebugInfo: false,
    
    // 反馈消息显示时间（毫秒）
    feedbackDuration: 3000,
  },
  
  // 开发模式配置
  development: {
    // 是否启用详细日志
    enableVerboseLogging: false,
    
    // 是否启用热重载
    enableHotReload: true,
  }
};

// 获取配置值的辅助函数
export function getConfig(path) {
  const keys = path.split('.');
  let value = APP_CONFIG;
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      return undefined;
    }
  }
  
  return value;
}

// 设置配置值的辅助函数
export function setConfig(path, newValue) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  let target = APP_CONFIG;
  
  for (const key of keys) {
    if (target && typeof target === 'object' && key in target) {
      target = target[key];
    } else {
      console.error(`配置路径不存在: ${path}`);
      return false;
    }
  }
  
  if (target && typeof target === 'object') {
    target[lastKey] = newValue;
    console.log(`配置已更新: ${path} = ${newValue}`);
    return true;
  }
  
  console.error(`无法设置配置: ${path}`);
  return false;
}

// 重置配置到默认值
export function resetConfig() {
  // 这里可以重置到默认配置
  console.log('配置已重置到默认值');
}

// 导出配置检查函数
export function isFeatureEnabled(feature) {
  return getConfig(feature) === true;
}

// 常用的配置检查函数
export const configChecks = {
  // 检查是否启用自动刷新
  isAutoRefreshEnabled: () => 
    isFeatureEnabled('dataSync.enableVisibilityRefresh') || 
    isFeatureEnabled('dataSync.enablePolling') || 
    isFeatureEnabled('dataSync.enableRealtimeSync'),
    
  // 检查是否启用缓存
  isCacheEnabled: () => 
    isFeatureEnabled('cache.enableMemoryCache') || 
    isFeatureEnabled('cache.enablePersistentCache'),
    
  // 检查是否为开发模式
  isDevelopmentMode: () => 
    isFeatureEnabled('development.enableVerboseLogging') || 
    isFeatureEnabled('development.enableHotReload'),
};

// 配置变更通知
const configChangeCallbacks = [];

export function onConfigChange(callback) {
  configChangeCallbacks.push(callback);
  
  // 返回取消监听的函数
  return () => {
    const index = configChangeCallbacks.indexOf(callback);
    if (index > -1) {
      configChangeCallbacks.splice(index, 1);
    }
  };
}

// 通知配置变更
function notifyConfigChange(path, oldValue, newValue) {
  configChangeCallbacks.forEach(callback => {
    try {
      callback(path, oldValue, newValue);
    } catch (error) {
      console.error('配置变更回调执行失败:', error);
    }
  });
}

// 增强的设置配置函数，支持变更通知
export function setConfigWithNotification(path, newValue) {
  const oldValue = getConfig(path);
  const success = setConfig(path, newValue);
  
  if (success && oldValue !== newValue) {
    notifyConfigChange(path, oldValue, newValue);
  }
  
  return success;
}

// 导出默认配置
export default APP_CONFIG;
