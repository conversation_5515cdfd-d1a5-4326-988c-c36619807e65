<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据恢复工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .current-data {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .backup-list {
            margin: 20px 0;
        }
        .backup-item {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .backup-info {
            flex-grow: 1;
        }
        .backup-filename {
            font-weight: bold;
            color: #333;
        }
        .backup-details {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 2px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        button.success {
            background-color: #28a745;
        }
        button.success:hover {
            background-color: #218838;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .manual-restore {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .manual-restore h3 {
            color: #856404;
            margin-top: 0;
        }
        textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据恢复工具</h1>
        
        <div class="alert alert-danger">
            <strong>警告：</strong>数据恢复操作将覆盖当前所有数据。请确保您了解此操作的后果。
        </div>

        <div class="current-data">
            <h3>当前数据状态</h3>
            <div id="current-status">正在加载...</div>
        </div>

        <div class="backup-list">
            <h3>可用备份文件</h3>
            <button onclick="loadBackups()" class="success">刷新备份列表</button>
            <div id="backup-list-content">
                <div class="loading">正在加载备份文件...</div>
            </div>
        </div>

        <div class="manual-restore">
            <h3>手动数据恢复</h3>
            <p>如果您有之前的数据备份，可以将JSON数据粘贴到下面的文本框中进行恢复：</p>
            <textarea id="manual-data" placeholder="请粘贴完整的JSON数据..."></textarea>
            <br><br>
            <button onclick="manualRestore()" class="danger">手动恢复数据</button>
        </div>

        <div id="result-message"></div>
    </div>

    <script>
        // 获取API基础URL
        function getApiBaseUrl() {
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;

            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                return 'http://localhost:3000/api';
            }

            if (hostname.includes('ngrok') || 
                hostname.includes('frp.wtf') || 
                hostname.includes('frp.one') || 
                hostname.includes('fucku.top') || 
                hostname.includes('idcfengye.com') ||
                hostname.includes('tunnel') ||
                hostname.includes('proxy')) {
                return '/api';
            }

            return `${protocol}//${hostname}:3000/api`;
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const resultDiv = document.getElementById('result-message');
            resultDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            setTimeout(() => {
                resultDiv.innerHTML = '';
            }, 5000);
        }

        // 加载当前数据状态
        async function loadCurrentStatus() {
            try {
                const apiUrl = getApiBaseUrl();
                const response = await fetch(`${apiUrl}/data`);
                
                if (response.ok) {
                    const data = await response.json();
                    const financeData = JSON.parse(data.vue_finance_data || '[]');
                    const productsData = JSON.parse(data.vue_products_data || '[]');
                    
                    document.getElementById('current-status').innerHTML = `
                        <p><strong>财务记录：</strong> ${financeData.length} 条</p>
                        <p><strong>产品数据：</strong> ${productsData.length} 个产品</p>
                        <p><strong>最后更新：</strong> ${new Date().toLocaleString()}</p>
                    `;
                } else {
                    document.getElementById('current-status').innerHTML = '<p style="color: red;">无法加载当前数据状态</p>';
                }
            } catch (error) {
                console.error('Error loading current status:', error);
                document.getElementById('current-status').innerHTML = '<p style="color: red;">加载失败</p>';
            }
        }

        // 加载备份文件列表
        async function loadBackups() {
            try {
                const apiUrl = getApiBaseUrl();
                const response = await fetch(`${apiUrl}/backups`);
                
                if (response.ok) {
                    const backups = await response.json();
                    const listDiv = document.getElementById('backup-list-content');
                    
                    if (backups.length === 0) {
                        listDiv.innerHTML = '<p>没有找到备份文件</p>';
                        return;
                    }
                    
                    listDiv.innerHTML = backups.map(backup => `
                        <div class="backup-item">
                            <div class="backup-info">
                                <div class="backup-filename">${backup.filename}</div>
                                <div class="backup-details">
                                    创建时间: ${new Date(backup.timestamp).toLocaleString()} | 
                                    文件大小: ${(backup.size / 1024).toFixed(2)} KB
                                </div>
                            </div>
                            <button onclick="restoreBackup('${backup.filename}')" class="danger">
                                恢复此备份
                            </button>
                        </div>
                    `).join('');
                } else {
                    document.getElementById('backup-list-content').innerHTML = '<p style="color: red;">无法加载备份文件列表</p>';
                }
            } catch (error) {
                console.error('Error loading backups:', error);
                document.getElementById('backup-list-content').innerHTML = '<p style="color: red;">加载失败</p>';
            }
        }

        // 恢复备份
        async function restoreBackup(filename) {
            if (!confirm(`确定要恢复备份文件 "${filename}" 吗？这将覆盖当前所有数据！`)) {
                return;
            }

            try {
                const apiUrl = getApiBaseUrl();
                const response = await fetch(`${apiUrl}/restore/${filename}`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showMessage(`数据恢复成功！已从 "${filename}" 恢复数据。`, 'success');
                    loadCurrentStatus();
                } else {
                    const error = await response.json();
                    showMessage(`恢复失败：${error.error}`, 'danger');
                }
            } catch (error) {
                console.error('Error restoring backup:', error);
                showMessage('恢复过程中发生错误', 'danger');
            }
        }

        // 手动恢复数据
        async function manualRestore() {
            const jsonData = document.getElementById('manual-data').value.trim();
            
            if (!jsonData) {
                showMessage('请输入要恢复的JSON数据', 'danger');
                return;
            }

            try {
                // 验证JSON格式
                const parsedData = JSON.parse(jsonData);
                
                if (!confirm('确定要使用手动输入的数据覆盖当前所有数据吗？')) {
                    return;
                }

                const apiUrl = getApiBaseUrl();
                
                // 逐个恢复每个数据键
                for (const [key, value] of Object.entries(parsedData)) {
                    const response = await fetch(`${apiUrl}/data/${key}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ value })
                    });
                    
                    if (!response.ok) {
                        throw new Error(`Failed to restore ${key}`);
                    }
                }
                
                showMessage('手动数据恢复成功！', 'success');
                loadCurrentStatus();
                document.getElementById('manual-data').value = '';
                
            } catch (error) {
                console.error('Error in manual restore:', error);
                showMessage(`手动恢复失败：${error.message}`, 'danger');
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            loadCurrentStatus();
            loadBackups();
        };
    </script>
</body>
</html>
