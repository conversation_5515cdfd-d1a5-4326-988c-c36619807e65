<template>
  <div v-if="showSettings" class="settings-overlay">
    <div class="settings-container">
      <div class="settings-header">
        <h3>应用设置</h3>
        <button @click="closeSettings" class="close-btn">×</button>
      </div>
      
      <div class="settings-content">
        <!-- 数据同步设置 -->
        <div class="settings-section">
          <h4>数据同步</h4>
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                v-model="localConfig.dataSync.enableVisibilityRefresh"
                @change="updateConfig('dataSync.enableVisibilityRefresh', $event.target.checked)"
              >
              页面切换时自动刷新
            </label>
            <p class="setting-description">当页面从后台切换回来时自动刷新数据</p>
          </div>
          
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                v-model="localConfig.dataSync.enablePolling"
                @change="updateConfig('dataSync.enablePolling', $event.target.checked)"
              >
              定时轮询
            </label>
            <p class="setting-description">定期从服务器检查数据更新</p>
          </div>
          
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                v-model="localConfig.dataSync.enableRealtimeSync"
                @change="updateConfig('dataSync.enableRealtimeSync', $event.target.checked)"
              >
              实时数据同步
            </label>
            <p class="setting-description">启用WebSocket实时数据同步</p>
          </div>
        </div>

        <!-- 缓存设置 -->
        <div class="settings-section">
          <h4>缓存设置</h4>
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                v-model="localConfig.cache.enableMemoryCache"
                @change="updateConfig('cache.enableMemoryCache', $event.target.checked)"
              >
              内存缓存
            </label>
            <p class="setting-description">启用内存缓存以提高响应速度</p>
          </div>
          
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                v-model="localConfig.cache.enablePersistentCache"
                @change="updateConfig('cache.enablePersistentCache', $event.target.checked)"
              >
              持久化缓存
            </label>
            <p class="setting-description">启用本地存储缓存，页面刷新后仍然有效</p>
          </div>
        </div>

        <!-- 界面设置 -->
        <div class="settings-section">
          <h4>界面设置</h4>
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                v-model="localConfig.ui.showPerformanceMonitor"
                @change="updateConfig('ui.showPerformanceMonitor', $event.target.checked)"
              >
              显示性能监控
            </label>
            <p class="setting-description">显示性能监控面板</p>
          </div>
          
          <div class="setting-item">
            <label class="setting-label">
              <input 
                type="checkbox" 
                v-model="localConfig.ui.showDebugInfo"
                @change="updateConfig('ui.showDebugInfo', $event.target.checked)"
              >
              显示调试信息
            </label>
            <p class="setting-description">在控制台显示详细的调试信息</p>
          </div>
        </div>

        <!-- 推荐设置 -->
        <div class="settings-section">
          <h4>推荐设置</h4>
          <div class="recommendation">
            <h5>🚀 最佳性能（推荐）</h5>
            <p>关闭所有自动刷新功能，启用缓存</p>
            <button @click="applyRecommendedSettings" class="apply-btn">应用推荐设置</button>
          </div>
        </div>

        <!-- 当前状态 -->
        <div class="settings-section">
          <h4>当前状态</h4>
          <div class="status-info">
            <div class="status-item">
              <span class="status-label">自动刷新:</span>
              <span :class="['status-value', autoRefreshEnabled ? 'enabled' : 'disabled']">
                {{ autoRefreshEnabled ? '启用' : '禁用' }}
              </span>
            </div>
            <div class="status-item">
              <span class="status-label">缓存:</span>
              <span :class="['status-value', cacheEnabled ? 'enabled' : 'disabled']">
                {{ cacheEnabled ? '启用' : '禁用' }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="settings-footer">
        <button @click="resetToDefaults" class="reset-btn">重置默认</button>
        <button @click="closeSettings" class="close-settings-btn">关闭</button>
      </div>
    </div>
  </div>

  <!-- 设置按钮 -->
  <button v-if="!showSettings" @click="openSettings" class="settings-toggle">
    ⚙️
  </button>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { APP_CONFIG, getConfig, setConfigWithNotification, configChecks } from '../config/appConfig';

// 设置面板显示状态
const showSettings = ref(false);

// 本地配置副本
const localConfig = ref({
  dataSync: { ...APP_CONFIG.dataSync },
  cache: { ...APP_CONFIG.cache },
  ui: { ...APP_CONFIG.ui }
});

// 计算属性
const autoRefreshEnabled = computed(() => configChecks.isAutoRefreshEnabled());
const cacheEnabled = computed(() => configChecks.isCacheEnabled());

// 打开设置
function openSettings() {
  showSettings.value = true;
  // 同步最新配置
  localConfig.value = {
    dataSync: { ...APP_CONFIG.dataSync },
    cache: { ...APP_CONFIG.cache },
    ui: { ...APP_CONFIG.ui }
  };
}

// 关闭设置
function closeSettings() {
  showSettings.value = false;
}

// 更新配置
function updateConfig(path, value) {
  setConfigWithNotification(path, value);
  console.log(`设置已更新: ${path} = ${value}`);
}

// 应用推荐设置
function applyRecommendedSettings() {
  // 关闭所有自动刷新
  updateConfig('dataSync.enableVisibilityRefresh', false);
  updateConfig('dataSync.enablePolling', false);
  updateConfig('dataSync.enableRealtimeSync', false);
  
  // 启用缓存
  updateConfig('cache.enableMemoryCache', true);
  updateConfig('cache.enablePersistentCache', true);
  
  // 关闭调试功能
  updateConfig('ui.showPerformanceMonitor', false);
  updateConfig('ui.showDebugInfo', false);
  
  // 更新本地配置
  localConfig.value = {
    dataSync: { ...APP_CONFIG.dataSync },
    cache: { ...APP_CONFIG.cache },
    ui: { ...APP_CONFIG.ui }
  };
  
  console.log('已应用推荐设置');
}

// 重置到默认值
function resetToDefaults() {
  // 重置所有配置到默认值
  Object.assign(APP_CONFIG.dataSync, {
    enableVisibilityRefresh: false,
    enablePolling: false,
    enableRealtimeSync: false,
    pollingInterval: 300000,
    visibilityRefreshThreshold: 60000,
  });
  
  Object.assign(APP_CONFIG.cache, {
    enableMemoryCache: true,
    enablePersistentCache: true,
    cacheExpiration: 300000,
  });
  
  Object.assign(APP_CONFIG.ui, {
    showPerformanceMonitor: false,
    showDebugInfo: false,
    feedbackDuration: 3000,
  });
  
  // 更新本地配置
  localConfig.value = {
    dataSync: { ...APP_CONFIG.dataSync },
    cache: { ...APP_CONFIG.cache },
    ui: { ...APP_CONFIG.ui }
  };
  
  console.log('配置已重置到默认值');
}

onMounted(() => {
  console.log('应用设置组件已加载');
});
</script>

<style scoped>
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.settings-container {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.settings-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:hover {
  background: #f0f0f0;
  color: #333;
}

.settings-content {
  padding: 20px;
}

.settings-section {
  margin-bottom: 30px;
}

.settings-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #2196F3;
  padding-bottom: 5px;
}

.setting-item {
  margin-bottom: 15px;
}

.setting-label {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.setting-label input[type="checkbox"] {
  margin-top: 2px;
  transform: scale(1.2);
}

.setting-description {
  margin: 5px 0 0 30px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.recommendation {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
}

.recommendation h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
}

.recommendation p {
  margin: 0 0 10px 0;
  font-size: 13px;
  color: #666;
}

.apply-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.apply-btn:hover {
  background: #45a049;
}

.status-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.status-label {
  font-size: 14px;
  color: #333;
}

.status-value {
  font-size: 14px;
  font-weight: 500;
}

.status-value.enabled {
  color: #4CAF50;
}

.status-value.disabled {
  color: #F44336;
}

.settings-footer {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  border-top: 1px solid #eee;
}

.reset-btn {
  background: #FF9800;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
}

.reset-btn:hover {
  background: #F57C00;
}

.close-settings-btn {
  background: #2196F3;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
}

.close-settings-btn:hover {
  background: #1976D2;
}

.settings-toggle {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #FF9800;
  color: white;
  border: none;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 999;
  transition: all 0.3s;
}

.settings-toggle:hover {
  background: #F57C00;
  transform: scale(1.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .settings-container {
    width: 95%;
    margin: 10px;
  }
  
  .settings-toggle {
    bottom: 70px;
    right: 10px;
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
}
</style>
