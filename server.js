import express from 'express';
import cors from 'cors';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createServer } from 'http';
import { Server } from 'socket.io';

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: "*", // 允许所有来源，生产环境中应该限制
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});
const port = 3001;

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 数据文件路径
const DATA_FILE = path.join(__dirname, 'store-data.json');

// 初始化数据文件（如果不存在）
if (!fs.existsSync(DATA_FILE)) {
  fs.writeFileSync(DATA_FILE, JSON.stringify({
    vue_products_data: '[]',
    vue_finance_data: '[]',
    vue_salespersons: '[]'
  }));
}

// 中间件
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
})); // 允许跨域请求
app.use(express.json()); // 解析JSON请求体
app.use(express.static('dist')); // 提供静态文件（构建后的Vue应用）

// 获取所有数据
app.get('/api/data', (_req, res) => {
  try {
    const data = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
    res.json(data);
  } catch (error) {
    console.error('Error reading data:', error);
    res.status(500).json({ error: 'Failed to read data' });
  }
});

// 获取特定键的数据
app.get('/api/data/:key', (req, res) => {
  try {
    const data = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
    const { key } = req.params;

    if (data[key] !== undefined) {
      res.json({ [key]: data[key] });
    } else {
      res.status(404).json({ error: 'Key not found' });
    }
  } catch (error) {
    console.error('Error reading data:', error);
    res.status(500).json({ error: 'Failed to read data' });
  }
});

// 设置数据
app.post('/api/data/:key', (req, res) => {
  try {
    const data = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
    const { key } = req.params;
    const { value } = req.body;

    if (value === undefined) {
      return res.status(400).json({ error: 'Value is required' });
    }

    data[key] = value;
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));

    // 发送数据更新通知
    io.emit('data-updated', { key, timestamp: Date.now() });
    console.log(`Data updated: ${key} at ${new Date().toISOString()}`);

    res.json({ success: true, key, message: 'Data saved successfully' });
  } catch (error) {
    console.error('Error saving data:', error);
    res.status(500).json({ error: 'Failed to save data' });
  }
});

// WebSocket 连接处理
io.on('connection', (socket) => {
  console.log(`🔗 [WebSocket] Client connected: ${socket.id}`);

  // 处理数据请求
  socket.on('request-data', (key) => {
    try {
      const data = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));

      if (key) {
        // 请求特定键的数据
        console.log(`📡 [WebSocket] Client ${socket.id} requested data for: ${key}`);
        if (data[key] !== undefined) {
          socket.emit('data-refresh', { key, value: data[key] });
        } else {
          console.warn(`⚠️ [WebSocket] Key ${key} not found`);
        }
      } else {
        // 请求所有数据
        console.log(`📡 [WebSocket] Client ${socket.id} requested all data`);
        socket.emit('data-refresh', { all: data });
      }
    } catch (error) {
      console.error('❌ [WebSocket] Error handling data request:', error);
      socket.emit('error', { message: 'Failed to fetch data' });
    }
  });

  // 客户端断开连接
  socket.on('disconnect', () => {
    console.log(`❌ [WebSocket] Client disconnected: ${socket.id}`);
  });
});

// 启动服务器
httpServer.listen(port, '0.0.0.0', () => {
  console.log(`Server running at http://0.0.0.0:${port}`);
  console.log(`Access from other devices using your local IP address`);
  console.log(`WebSocket server is also running on the same port`);
});
