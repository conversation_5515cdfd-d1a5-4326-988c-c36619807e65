import express from 'express';
import cors from 'cors';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createServer } from 'http';
import { Server } from 'socket.io';

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: "*", // 允许所有来源，生产环境中应该限制
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});
const port = 3000;

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 数据文件路径
const DATA_FILE = path.join(__dirname, 'store-data.json');
const BACKUP_DIR = path.join(__dirname, 'backups');

// 确保备份目录存在
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR);
}

// 创建数据备份
function createBackup() {
  try {
    if (fs.existsSync(DATA_FILE)) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFile = path.join(BACKUP_DIR, `store-data-${timestamp}.json`);
      fs.copyFileSync(DATA_FILE, backupFile);
      console.log(`Backup created: ${backupFile}`);

      // 只保留最近10个备份文件
      const backupFiles = fs.readdirSync(BACKUP_DIR)
        .filter(file => file.startsWith('store-data-') && file.endsWith('.json'))
        .sort()
        .reverse();

      if (backupFiles.length > 10) {
        const filesToDelete = backupFiles.slice(10);
        filesToDelete.forEach(file => {
          fs.unlinkSync(path.join(BACKUP_DIR, file));
          console.log(`Old backup deleted: ${file}`);
        });
      }
    }
  } catch (error) {
    console.error('Error creating backup:', error);
  }
}

// 初始化数据文件（如果不存在）
if (!fs.existsSync(DATA_FILE)) {
  fs.writeFileSync(DATA_FILE, JSON.stringify({
    vue_products_data: '[]',
    vue_finance_data: '[]',
    vue_salespersons: '[]'
  }));
}

// 中间件
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
})); // 允许跨域请求
app.use(express.json()); // 解析JSON请求体
app.use(express.static('dist')); // 提供静态文件（构建后的Vue应用）

// 获取所有数据
app.get('/api/data', (_req, res) => {
  try {
    const data = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
    res.json(data);
  } catch (error) {
    console.error('Error reading data:', error);
    res.status(500).json({ error: 'Failed to read data' });
  }
});

// 获取特定键的数据
app.get('/api/data/:key', (req, res) => {
  try {
    const data = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
    const { key } = req.params;

    if (data[key] !== undefined) {
      res.json({ [key]: data[key] });
    } else {
      res.status(404).json({ error: 'Key not found' });
    }
  } catch (error) {
    console.error('Error reading data:', error);
    res.status(500).json({ error: 'Failed to read data' });
  }
});

// 设置数据
app.post('/api/data/:key', (req, res) => {
  try {
    const data = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
    const { key } = req.params;
    const { value } = req.body;

    if (value === undefined) {
      return res.status(400).json({ error: 'Value is required' });
    }

    // 在修改数据前创建备份
    createBackup();

    data[key] = value;
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));

    // 发送数据更新通知
    io.emit('data-updated', { key, timestamp: Date.now() });
    console.log(`Data updated: ${key} at ${new Date().toISOString()}`);

    res.json({ success: true, key, message: 'Data saved successfully' });
  } catch (error) {
    console.error('Error saving data:', error);
    res.status(500).json({ error: 'Failed to save data' });
  }
});

// 获取备份文件列表
app.get('/api/backups', (_req, res) => {
  try {
    if (!fs.existsSync(BACKUP_DIR)) {
      return res.json([]);
    }

    const backupFiles = fs.readdirSync(BACKUP_DIR)
      .filter(file => file.startsWith('store-data-') && file.endsWith('.json'))
      .map(file => {
        const filePath = path.join(BACKUP_DIR, file);
        const stats = fs.statSync(filePath);
        return {
          filename: file,
          timestamp: stats.mtime,
          size: stats.size
        };
      })
      .sort((a, b) => b.timestamp - a.timestamp);

    res.json(backupFiles);
  } catch (error) {
    console.error('Error reading backups:', error);
    res.status(500).json({ error: 'Failed to read backups' });
  }
});

// 恢复备份
app.post('/api/restore/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    const backupFile = path.join(BACKUP_DIR, filename);

    if (!fs.existsSync(backupFile)) {
      return res.status(404).json({ error: 'Backup file not found' });
    }

    // 在恢复前创建当前数据的备份
    createBackup();

    // 恢复备份文件
    fs.copyFileSync(backupFile, DATA_FILE);

    // 发送数据更新通知
    io.emit('data-updated', { key: 'all', timestamp: Date.now() });
    console.log(`Data restored from backup: ${filename} at ${new Date().toISOString()}`);

    res.json({ success: true, message: 'Data restored successfully', filename });
  } catch (error) {
    console.error('Error restoring backup:', error);
    res.status(500).json({ error: 'Failed to restore backup' });
  }
});

// 启动服务器
httpServer.listen(port, '0.0.0.0', () => {
  console.log(`Server running at http://0.0.0.0:${port}`);
  console.log(`Access from other devices using your local IP address`);
  console.log(`WebSocket server is also running on the same port`);
});
