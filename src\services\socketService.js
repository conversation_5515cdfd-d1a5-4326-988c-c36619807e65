// socketService.js - 提供实时数据同步功能

import { io } from 'socket.io-client';
import { ref } from 'vue';
import { useServerStorage } from './serverStorage';

// 获取服务器 API 地址
const getSocketUrl = () => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const hostname = window.location.hostname;

  // 如果是在开发环境中
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'ws://localhost:3000';
  }

  // 如果是通过ngrok访问
  if (hostname.includes('ngrok')) {
    // 使用相对路径，这样WebSocket会连接到当前域名
    return '/';
  }

  // 如果是通过IP访问（例如从iPad访问）
  return `${protocol}//${hostname}:3000`;
};

// 创建 Socket.IO 连接
const socket = io(getSocketUrl());

// 连接状态
const connected = ref(false);
const lastUpdate = ref(null);

// 数据更新回调函数
const dataUpdateCallbacks = {
  'vue_products_data': [],
  'vue_finance_data': [],
  'vue_salespersons': []
};

// 连接事件处理
socket.on('connect', () => {
  console.log('🔗 [WebSocket] Connected to server:', socket.id);
  connected.value = true;
  lastUpdate.value = new Date();

  // 连接后请求最新数据
  console.log('📡 [WebSocket] Requesting latest data...');
  socket.emit('request-data');
});

// 断开连接事件处理
socket.on('disconnect', () => {
  console.log('❌ [WebSocket] Disconnected from server');
  connected.value = false;
});

// 连接错误处理
socket.on('connect_error', (error) => {
  console.error('❌ [WebSocket] Connection error:', error);
  connected.value = false;
});

// 重连事件处理
socket.on('reconnect', (attemptNumber) => {
  console.log(`🔄 [WebSocket] Reconnected after ${attemptNumber} attempts`);
  connected.value = true;
  lastUpdate.value = new Date();
});

socket.on('reconnect_attempt', (attemptNumber) => {
  console.log(`🔄 [WebSocket] Reconnection attempt ${attemptNumber}`);
});

socket.on('reconnect_error', (error) => {
  console.error('❌ [WebSocket] Reconnection error:', error);
});

socket.on('reconnect_failed', () => {
  console.error('❌ [WebSocket] Reconnection failed');
  connected.value = false;
});

// 数据更新事件处理
socket.on('data-updated', async (data) => {
  console.log('📢 [WebSocket] Data updated notification received:', data);
  lastUpdate.value = new Date();

  // 如果收到特定键的更新通知
  if (data.key) {
    console.log(`🔄 [WebSocket] Fetching updated data for ${data.key}...`);

    // 从服务器获取最新数据
    const { getData } = useServerStorage();
    const updatedData = await getData(data.key);

    console.log(`📦 [WebSocket] Received updated data for ${data.key}:`, typeof updatedData === 'string' ? `${updatedData.length} chars` : 'object');

    // 调用注册的回调函数
    if (dataUpdateCallbacks[data.key] && dataUpdateCallbacks[data.key].length > 0) {
      console.log(`📢 [WebSocket] Notifying ${dataUpdateCallbacks[data.key].length} callbacks for ${data.key}`);
      dataUpdateCallbacks[data.key].forEach((callback, index) => {
        try {
          console.log(`🔄 [WebSocket] Executing callback ${index + 1} for ${data.key}...`);
          callback(updatedData);
          console.log(`✅ [WebSocket] Callback ${index + 1} executed successfully for ${data.key}`);
        } catch (error) {
          console.error(`❌ [WebSocket] Error in callback ${index + 1} for ${data.key}:`, error);
        }
      });
    } else {
      console.warn(`⚠️ [WebSocket] No callbacks registered for ${data.key}`);
    }
  }
});

// 数据刷新事件处理
socket.on('data-refresh', (data) => {
  console.log('Data refresh received:', data);

  // 如果收到特定键的数据
  if (data.key && data.value) {
    if (dataUpdateCallbacks[data.key]) {
      dataUpdateCallbacks[data.key].forEach(callback => {
        try {
          callback(data.value);
        } catch (error) {
          console.error('Error in data refresh callback:', error);
        }
      });
    }
  }

  // 如果收到所有数据
  if (data.all) {
    Object.keys(data.all).forEach(key => {
      if (dataUpdateCallbacks[key]) {
        dataUpdateCallbacks[key].forEach(callback => {
          try {
            callback(data.all[key]);
          } catch (error) {
            console.error('Error in data refresh callback:', error);
          }
        });
      }
    });
  }
});

// 注册数据更新回调函数
function onDataUpdate(key, callback) {
  if (!dataUpdateCallbacks[key]) {
    dataUpdateCallbacks[key] = [];
  }
  dataUpdateCallbacks[key].push(callback);

  console.log(`📝 [WebSocket] Registered callback for ${key}. Total callbacks: ${dataUpdateCallbacks[key].length}`);

  // 返回取消注册函数
  return () => {
    if (dataUpdateCallbacks[key]) {
      const index = dataUpdateCallbacks[key].indexOf(callback);
      if (index !== -1) {
        dataUpdateCallbacks[key].splice(index, 1);
        console.log(`🗑️ [WebSocket] Unregistered callback for ${key}. Remaining callbacks: ${dataUpdateCallbacks[key].length}`);
      }
    }
  };
}

// 请求最新数据
function requestLatestData(key = null) {
  if (connected.value) {
    console.log(`📡 [WebSocket] Requesting latest data${key ? ` for ${key}` : ' (all)'}...`);
    socket.emit('request-data', key);
  } else {
    console.warn('⚠️ [WebSocket] Not connected to server, cannot request data');
  }
}

export const useSocket = () => {
  return {
    connected,
    lastUpdate,
    onDataUpdate,
    requestLatestData
  };
};
