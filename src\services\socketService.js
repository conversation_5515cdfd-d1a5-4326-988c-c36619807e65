// socketService.js - 提供实时数据同步功能

import { io } from 'socket.io-client';
import { ref } from 'vue';
import { useServerStorage } from './serverStorage';

// 获取服务器 API 地址
const getSocketUrl = () => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const hostname = window.location.hostname;
  const port = window.location.port;

  console.log('🔍 [WebSocket] Current location:', {
    protocol: window.location.protocol,
    hostname,
    port,
    href: window.location.href
  });

  // 如果是在开发环境中（localhost）
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    const socketUrl = 'ws://localhost:3000';
    console.log('🔗 [WebSocket] Using localhost URL:', socketUrl);
    return socketUrl;
  }

  // 如果是通过内网穿透或其他域名访问
  let socketUrl;

  // 检查是否是内网穿透域名
  const isTunnelDomain = hostname.includes('ngrok') ||
                        hostname.includes('tunnel') ||
                        hostname.includes('localtunnel') ||
                        hostname.includes('serveo') ||
                        hostname.includes('pagekite') ||
                        hostname.endsWith('.io') ||
                        hostname.endsWith('.me') ||
                        hostname.endsWith('.run.app');

  if (isTunnelDomain) {
    // 内网穿透：使用当前页面的完整URL（不指定端口）
    socketUrl = `${protocol}//${hostname}`;
    console.log('🌐 [WebSocket] Detected tunnel domain, using current domain');
  } else if (port && port !== '80' && port !== '443') {
    // 如果当前页面有端口号，使用相同的端口
    socketUrl = `${protocol}//${hostname}:${port}`;
  } else {
    // 如果没有端口号或是标准端口，尝试连接到3000端口
    socketUrl = `${protocol}//${hostname}:3000`;
  }

  console.log('🔗 [WebSocket] Using URL:', socketUrl);
  return socketUrl;
};

// 创建 Socket.IO 连接，配置重连选项
const socket = io(getSocketUrl(), {
  // 连接超时时间
  timeout: 5000,
  // 重连配置
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  // 传输方式
  transports: ['websocket', 'polling'],
  // 强制使用新连接
  forceNew: true
});

// 连接状态
const connected = ref(false);
const lastUpdate = ref(null);
const connectionMode = ref('websocket'); // 'websocket' 或 'polling'
const isUsingFallback = ref(false);

// 数据更新回调函数
const dataUpdateCallbacks = {
  'vue_products_data': [],
  'vue_finance_data': [],
  'vue_salespersons': []
};

// 连接事件处理
socket.on('connect', () => {
  console.log('🔗 [WebSocket] Connected to server:', socket.id);
  connected.value = true;
  lastUpdate.value = new Date();

  // 停止轮询回退（如果正在运行）
  stopPollingFallback();

  // 连接后请求最新数据
  console.log('📡 [WebSocket] Requesting latest data...');
  socket.emit('request-data');
});

// 断开连接事件处理
socket.on('disconnect', () => {
  console.log('❌ [WebSocket] Disconnected from server');
  connected.value = false;
});

// 连接错误处理
socket.on('connect_error', (error) => {
  console.error('❌ [WebSocket] Connection error:', error);
  connected.value = false;

  // 如果还没有启动轮询回退，则启动
  if (!isUsingFallback.value) {
    console.log('🔄 [WebSocket] Connection failed, starting polling fallback');
    setTimeout(() => {
      if (!connected.value) {
        startPollingFallback();
      }
    }, 2000); // 2秒后启动轮询回退
  }
});

// 重连事件处理
socket.on('reconnect', (attemptNumber) => {
  console.log(`🔄 [WebSocket] Reconnected after ${attemptNumber} attempts`);
  connected.value = true;
  lastUpdate.value = new Date();

  // 停止轮询回退
  stopPollingFallback();
});

socket.on('reconnect_attempt', (attemptNumber) => {
  console.log(`🔄 [WebSocket] Reconnection attempt ${attemptNumber}`);
});

socket.on('reconnect_error', (error) => {
  console.error('❌ [WebSocket] Reconnection error:', error);
});

socket.on('reconnect_failed', () => {
  console.error('❌ [WebSocket] Reconnection failed, switching to polling mode');
  connected.value = false;
  startPollingFallback();
});

// 轮询回退机制
let pollingTimer = null;
const POLLING_INTERVAL = 3000; // 3秒轮询间隔

function startPollingFallback() {
  if (pollingTimer) return; // 避免重复启动

  console.log('🔄 [WebSocket] Starting polling fallback mode');
  isUsingFallback.value = true;
  connectionMode.value = 'polling';

  // 立即执行一次轮询
  pollForUpdates();

  // 设置定时轮询
  pollingTimer = setInterval(pollForUpdates, POLLING_INTERVAL);
}

function stopPollingFallback() {
  if (pollingTimer) {
    clearInterval(pollingTimer);
    pollingTimer = null;
    console.log('⏹️ [WebSocket] Stopped polling fallback mode');
  }
  isUsingFallback.value = false;
  connectionMode.value = 'websocket';
}

async function pollForUpdates() {
  try {
    // 模拟连接状态
    connected.value = true;
    lastUpdate.value = new Date();

    // 轮询所有数据键
    const keys = ['vue_products_data', 'vue_finance_data', 'vue_salespersons'];

    for (const key of keys) {
      if (dataUpdateCallbacks[key] && dataUpdateCallbacks[key].length > 0) {
        // 从服务器获取最新数据
        const { getData } = useServerStorage();
        const data = await getData(key);

        if (data) {
          // 调用回调函数
          dataUpdateCallbacks[key].forEach(callback => {
            try {
              callback(data);
            } catch (error) {
              console.error(`❌ [Polling] Error in callback for ${key}:`, error);
            }
          });
        }
      }
    }
  } catch (error) {
    console.error('❌ [Polling] Error during polling:', error);
    connected.value = false;
  }
}

// 数据更新事件处理
socket.on('data-updated', async (data) => {
  console.log('📢 [WebSocket] Data updated notification received:', data);
  lastUpdate.value = new Date();

  // 如果收到特定键的更新通知
  if (data.key) {
    // 添加短暂延迟，让本地保存操作完成
    setTimeout(async () => {
      console.log(`🔄 [WebSocket] Fetching updated data for ${data.key}...`);

      // 从服务器获取最新数据
      const { getData } = useServerStorage();
      const updatedData = await getData(data.key);

      console.log(`📦 [WebSocket] Received updated data for ${data.key}:`, typeof updatedData === 'string' ? `${updatedData.length} chars` : 'object');

      // 调用注册的回调函数
      if (dataUpdateCallbacks[data.key] && dataUpdateCallbacks[data.key].length > 0) {
        console.log(`📢 [WebSocket] Notifying ${dataUpdateCallbacks[data.key].length} callbacks for ${data.key}`);
        dataUpdateCallbacks[data.key].forEach((callback, index) => {
          try {
            console.log(`🔄 [WebSocket] Executing callback ${index + 1} for ${data.key}...`);
            callback(updatedData);
            console.log(`✅ [WebSocket] Callback ${index + 1} executed successfully for ${data.key}`);
          } catch (error) {
            console.error(`❌ [WebSocket] Error in callback ${index + 1} for ${data.key}:`, error);
          }
        });
      } else {
        console.warn(`⚠️ [WebSocket] No callbacks registered for ${data.key}`);
      }
    }, 100); // 100ms 延迟
  }
});

// 数据刷新事件处理
socket.on('data-refresh', (data) => {
  console.log('Data refresh received:', data);
  lastUpdate.value = new Date(); // 更新最后更新时间

  // 如果收到特定键的数据
  if (data.key && data.value) {
    if (dataUpdateCallbacks[data.key]) {
      dataUpdateCallbacks[data.key].forEach(callback => {
        try {
          callback(data.value);
        } catch (error) {
          console.error('Error in data refresh callback:', error);
        }
      });
    }
  }

  // 如果收到所有数据
  if (data.all) {
    Object.keys(data.all).forEach(key => {
      if (dataUpdateCallbacks[key]) {
        dataUpdateCallbacks[key].forEach(callback => {
          try {
            callback(data.all[key]);
          } catch (error) {
            console.error('Error in data refresh callback:', error);
          }
        });
      }
    });
  }
});

// 注册数据更新回调函数
function onDataUpdate(key, callback) {
  if (!dataUpdateCallbacks[key]) {
    dataUpdateCallbacks[key] = [];
  }
  dataUpdateCallbacks[key].push(callback);

  console.log(`📝 [WebSocket] Registered callback for ${key}. Total callbacks: ${dataUpdateCallbacks[key].length}`);

  // 返回取消注册函数
  return () => {
    if (dataUpdateCallbacks[key]) {
      const index = dataUpdateCallbacks[key].indexOf(callback);
      if (index !== -1) {
        dataUpdateCallbacks[key].splice(index, 1);
        console.log(`🗑️ [WebSocket] Unregistered callback for ${key}. Remaining callbacks: ${dataUpdateCallbacks[key].length}`);
      }
    }
  };
}

// 请求最新数据
function requestLatestData(key = null) {
  if (connected.value) {
    console.log(`📡 [WebSocket] Requesting latest data${key ? ` for ${key}` : ' (all)'}...`);
    socket.emit('request-data', key);
  } else {
    console.warn('⚠️ [WebSocket] Not connected to server, cannot request data');
  }
}

export const useSocket = () => {
  return {
    connected,
    lastUpdate,
    connectionMode,
    isUsingFallback,
    onDataUpdate,
    requestLatestData
  };
};
