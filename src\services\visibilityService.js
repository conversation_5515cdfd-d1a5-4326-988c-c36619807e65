import { ref, onMounted, onUnmounted } from 'vue';

// 页面可见性状态
const isPageVisible = ref(true);
const lastVisibleTime = ref(Date.now());
const isFirstLoad = ref(true);

// 页面可见性变化的回调函数列表
const visibilityCallbacks = [];

// 添加页面可见性变化的监听器
function addVisibilityCallback(callback) {
  visibilityCallbacks.push(callback);
}

// 移除页面可见性变化的监听器
function removeVisibilityCallback(callback) {
  const index = visibilityCallbacks.indexOf(callback);
  if (index > -1) {
    visibilityCallbacks.splice(index, 1);
  }
}

// 处理页面可见性变化
function handleVisibilityChange() {
  const currentTime = Date.now();
  const wasVisible = isPageVisible.value;
  
  if (document.hidden) {
    // 页面变为不可见
    isPageVisible.value = false;
    lastVisibleTime.value = currentTime;
    console.log('页面变为不可见');
  } else {
    // 页面变为可见
    isPageVisible.value = true;
    const hiddenDuration = currentTime - lastVisibleTime.value;
    
    console.log(`页面变为可见，隐藏时长: ${hiddenDuration}ms`);
    
    // 如果不是首次加载，且隐藏时间超过30秒，才触发刷新回调
    if (!isFirstLoad.value && hiddenDuration > 30000) {
      console.log('页面隐藏时间较长，触发数据刷新');
      visibilityCallbacks.forEach(callback => {
        try {
          callback(hiddenDuration);
        } catch (error) {
          console.error('执行可见性回调时出错:', error);
        }
      });
    }
    
    // 标记不再是首次加载
    if (isFirstLoad.value) {
      isFirstLoad.value = false;
    }
  }
}

// 防止页面在后台时被系统回收的策略
function preventPageReclaim() {
  // 使用 Page Visibility API
  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  // 使用 beforeunload 事件保存状态
  window.addEventListener('beforeunload', () => {
    // 保存当前时间戳到 sessionStorage
    sessionStorage.setItem('lastActiveTime', Date.now().toString());
  });
  
  // 页面加载时检查是否是从后台恢复
  const lastActiveTime = sessionStorage.getItem('lastActiveTime');
  if (lastActiveTime) {
    const timeDiff = Date.now() - parseInt(lastActiveTime);
    console.log(`页面恢复，距离上次活跃时间: ${timeDiff}ms`);
    
    // 如果时间差超过5分钟，可能需要刷新数据
    if (timeDiff > 300000) { // 5分钟
      console.log('页面可能已被系统回收，建议刷新数据');
    }
  }
}

// 获取页面状态信息
function getPageStatus() {
  return {
    isVisible: isPageVisible.value,
    lastVisibleTime: lastVisibleTime.value,
    isFirstLoad: isFirstLoad.value,
    hiddenDuration: isPageVisible.value ? 0 : Date.now() - lastVisibleTime.value
  };
}

// 手动标记页面为活跃状态（用于用户交互时）
function markPageActive() {
  if (!isPageVisible.value) {
    isPageVisible.value = true;
  }
  lastVisibleTime.value = Date.now();
}

// 组合式函数
export function usePageVisibility() {
  onMounted(() => {
    preventPageReclaim();
  });
  
  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  });
  
  return {
    isPageVisible,
    lastVisibleTime,
    isFirstLoad,
    addVisibilityCallback,
    removeVisibilityCallback,
    getPageStatus,
    markPageActive
  };
}

// 导出单例服务
export const pageVisibilityService = {
  isPageVisible,
  lastVisibleTime,
  isFirstLoad,
  addVisibilityCallback,
  removeVisibilityCallback,
  getPageStatus,
  markPageActive,
  preventPageReclaim
};
