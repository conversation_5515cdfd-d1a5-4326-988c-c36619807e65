<template>
  <tr>
    <td>{{ product.name }}</td>
    <td>￥{{ formatPrice(product.price) }}</td>
    <td>{{ product.available }}</td>
    <td>{{ product.remaining }}</td>
    <td class="action-cell">
      <button class="sell-btn" @click="emit('sell-product', product.id)" :disabled="product.remaining <= 0">-1</button>
    </td>
    <td class="action-cell">
      <button
        class="restock-btn"
        @click="emit('restock-product', product.id)"
        :disabled="product.remaining === product.available">
        补货
      </button>
      <button class="edit-btn" @click="emit('edit-product', product)">编辑</button>
    </td>
  </tr>
</template>

<script setup>
const props = defineProps({
  product: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['sell-product', 'restock-product', 'edit-product', 'delete-product']);

function formatPrice(price) {
  return typeof price === 'number' ? price.toFixed(2) : '0.00';
}
</script>

<style scoped>
button {
    padding: 4px 8px; /* Smaller buttons for table */
    font-size: 0.9em;
    margin: 0 3px;
}

.sell-btn {
    background-color: #28a745;
    color: white; /* Make text visible */
}
.sell-btn:hover:not(:disabled) {
    background-color: #218838;
}
.sell-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.edit-btn {
    background-color: #ffc107;
    color: #333;
}
.edit-btn:hover {
    background-color: #e0a800;
}

/* If delete button was here */
/* .delete-btn {
    background-color: #dc3545;
}
.delete-btn:hover {
    background-color: #c82333;
} */

/* 新增：居中对齐操作单元格 */
.action-cell {
    text-align: center;
}

/* 补货按钮样式 (可以自定义) */
.restock-btn {
    background-color: #17a2b8; /* Teal/Info color */
    color: white;
}
.restock-btn:hover:not(:disabled) {
    background-color: #138496;
}
.restock-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}
</style>