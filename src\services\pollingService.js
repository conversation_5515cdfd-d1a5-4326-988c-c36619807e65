// pollingService.js - 提供基于轮询的数据同步功能

import { ref, onMounted, onUnmounted } from 'vue';
import { useServerStorage } from './serverStorage';

// 获取服务器存储服务
const { getData } = useServerStorage();

// 连接状态
const connected = ref(false);
const lastUpdate = ref(null);
const connectionCheckCount = ref(0);

// 数据更新回调函数
const dataUpdateCallbacks = {
  'vue_products_data': [],
  'vue_finance_data': [],
  'vue_salespersons': []
};

// 上次数据版本（用于检测变化）
const lastDataVersions = {
  'vue_products_data': null,
  'vue_finance_data': null,
  'vue_salespersons': null
};

// 轮询间隔（毫秒）
const POLLING_INTERVAL = 2000; // 2秒
const CONNECTION_CHECK_INTERVAL = 5000; // 5秒

// 轮询定时器
let pollingTimer = null;
let connectionCheckTimer = null;

// 检查服务器连接
async function checkConnection() {
  try {
    // 获取API基础URL
    let apiUrl;
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;

    console.log(`[Connection Check] Hostname: ${hostname}`);

    // 如果是通过ngrok或frp访问
    if (hostname.includes('ngrok') || hostname.includes('frp.wtf') || hostname.includes('idcfengye.com')) {
      apiUrl = '/api/data';
      console.log('[Connection Check] Using relative API URL for tunneled access');
    } else if (hostname === 'localhost' || hostname === '127.0.0.1') {
      apiUrl = 'http://localhost:3000/api/data';
      console.log('[Connection Check] Using localhost API URL');
    } else {
      apiUrl = `${protocol}//${hostname}:3000/api/data`;
      console.log(`[Connection Check] Using IP-based API URL: ${apiUrl}`);
    }

    // 添加时间戳防止缓存
    const urlWithTimestamp = `${apiUrl}?t=${Date.now()}`;
    console.log(`[Connection Check] Checking connection to: ${urlWithTimestamp}`);

    // 尝试从服务器获取任何数据，设置超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

    const response = await fetch(urlWithTimestamp, {
      signal: controller.signal,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      if (!connected.value) {
        console.log('[Connection Check] Server connection established');
        connected.value = true;
        // 连接恢复后立即获取最新数据
        pollAllData();
      }
      connectionCheckCount.value = 0;
    } else {
      console.warn(`[Connection Check] Server returned status: ${response.status}`);
      connectionCheckCount.value++;
      if (connectionCheckCount.value > 3) {
        connected.value = false;
      }
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('[Connection Check] Connection timeout');
    } else {
      console.error('[Connection Check] Server connection check failed:', error);
    }
    connectionCheckCount.value++;
    if (connectionCheckCount.value > 3) {
      connected.value = false;
    }
  }
}

// 开始轮询
function startPolling() {
  // 停止现有的轮询
  stopPolling();

  // 立即检查连接
  checkConnection();

  // 设置连接检查定时器
  connectionCheckTimer = setInterval(checkConnection, CONNECTION_CHECK_INTERVAL); // 每5秒检查一次连接

  // 立即执行一次数据轮询
  pollAllData();

  // 设置数据轮询定时器
  pollingTimer = setInterval(pollAllData, POLLING_INTERVAL);
  console.log('Data polling started');
}

// 停止轮询
function stopPolling() {
  if (pollingTimer) {
    clearInterval(pollingTimer);
    pollingTimer = null;
  }

  if (connectionCheckTimer) {
    clearInterval(connectionCheckTimer);
    connectionCheckTimer = null;
  }

  console.log('Data polling stopped');
}

// 轮询所有数据
async function pollAllData() {
  // 如果未连接，不进行轮询
  if (!connected.value) {
    return;
  }

  try {
    // 轮询产品数据
    await pollData('vue_products_data');
    // 轮询财务数据
    await pollData('vue_finance_data');
    // 轮询售货员数据
    await pollData('vue_salespersons');
  } catch (error) {
    console.error('Error polling data:', error);
    // 不在这里更新连接状态，让连接检查器处理
  }
}

// 轮询特定数据
async function pollData(key) {
  try {
    // 从服务器获取数据
    const data = await getData(key);

    // 如果数据为空，不处理
    if (!data) return;

    // 检查数据是否有变化
    const dataHash = hashData(data);
    if (dataHash !== lastDataVersions[key]) {
      console.log(`Data changed for ${key}`);
      lastDataVersions[key] = dataHash;
      lastUpdate.value = new Date();

      // 调用注册的回调函数
      if (dataUpdateCallbacks[key]) {
        dataUpdateCallbacks[key].forEach(callback => {
          try {
            callback(data);
          } catch (error) {
            console.error('Error in data update callback:', error);
          }
        });
      }
    }
  } catch (error) {
    console.error(`Error polling data for ${key}:`, error);
    // 不抛出错误，让轮询继续进行
  }
}

// 简单的数据哈希函数（用于检测变化）
function hashData(data) {
  if (typeof data !== 'string') {
    return JSON.stringify(data);
  }
  return data;
}

// 注册数据更新回调函数
function onDataUpdate(key, callback) {
  if (!dataUpdateCallbacks[key]) {
    dataUpdateCallbacks[key] = [];
  }
  dataUpdateCallbacks[key].push(callback);

  // 返回取消注册函数
  return () => {
    if (dataUpdateCallbacks[key]) {
      const index = dataUpdateCallbacks[key].indexOf(callback);
      if (index !== -1) {
        dataUpdateCallbacks[key].splice(index, 1);
      }
    }
  };
}

// 请求最新数据
function requestLatestData(key = null) {
  if (key) {
    pollData(key).catch(error => {
      console.error(`Error requesting latest data for ${key}:`, error);
    });
  } else {
    pollAllData().catch(error => {
      console.error('Error requesting latest data:', error);
    });
  }
}

// 创建一个初始化函数
function initPolling() {
  // 自动启动轮询
  startPolling();

  // 页面卸载时停止轮询
  window.addEventListener('beforeunload', () => {
    stopPolling();
  });
}

// 立即初始化
initPolling();

// 导出钩子函数
export const usePolling = () => {
  // 组件挂载时确保轮询已启动
  onMounted(() => {
    if (!pollingTimer) {
      startPolling();
    }
  });

  // 组件卸载时不停止轮询，因为可能有其他组件仍在使用

  return {
    connected,
    lastUpdate,
    onDataUpdate,
    requestLatestData,
    startPolling,
    stopPolling
  };
};
