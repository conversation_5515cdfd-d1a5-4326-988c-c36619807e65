<template>
  <div v-if="showMonitor" class="performance-monitor">
    <div class="monitor-header">
      <h4>性能监控</h4>
      <button @click="toggleMonitor" class="close-btn">×</button>
    </div>
    
    <div class="monitor-content">
      <!-- 页面可见性状态 -->
      <div class="status-section">
        <h5>页面状态</h5>
        <div class="status-item">
          <span class="label">页面可见:</span>
          <span :class="['status', isPageVisible ? 'visible' : 'hidden']">
            {{ isPageVisible ? '可见' : '隐藏' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">隐藏时长:</span>
          <span class="value">{{ hiddenDuration }}</span>
        </div>
      </div>

      <!-- 缓存统计 -->
      <div class="cache-section">
        <h5>缓存统计</h5>
        <div class="cache-item">
          <span class="label">命中率:</span>
          <span class="value">{{ cacheStats.hitRate }}</span>
        </div>
        <div class="cache-item">
          <span class="label">缓存大小:</span>
          <span class="value">{{ cacheStats.size }}/{{ cacheStats.maxSize }}</span>
        </div>
        <div class="cache-item">
          <span class="label">命中次数:</span>
          <span class="value">{{ cacheStats.hits }}</span>
        </div>
        <div class="cache-item">
          <span class="label">未命中:</span>
          <span class="value">{{ cacheStats.misses }}</span>
        </div>
        <div class="cache-item">
          <span class="label">缓存时长:</span>
          <span class="value">{{ cacheStats.duration }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <button @click="clearCache" class="action-btn clear-btn">清空缓存</button>
        <button @click="refreshStats" class="action-btn refresh-btn">刷新统计</button>
      </div>
    </div>
  </div>

  <!-- 浮动按钮 -->
  <button v-if="!showMonitor" @click="toggleMonitor" class="monitor-toggle">
    📊
  </button>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { usePageVisibility } from '../services/visibilityService';
import { useServerStorage } from '../services/serverStorage';

// 监控面板显示状态
const showMonitor = ref(false);

// 获取页面可见性服务
const { isPageVisible, getPageStatus } = usePageVisibility();

// 获取服务器存储服务（包含缓存功能）
const { getCacheStats, clearCache: clearCacheService } = useServerStorage();

// 缓存统计数据
const cacheStats = ref({
  hits: 0,
  misses: 0,
  size: 0,
  hitRate: '0%',
  duration: '0秒',
  maxSize: 0
});

// 计算隐藏时长
const hiddenDuration = computed(() => {
  const status = getPageStatus();
  if (status.isVisible) {
    return '0秒';
  }
  
  const duration = status.hiddenDuration;
  if (duration < 1000) {
    return `${duration}毫秒`;
  } else if (duration < 60000) {
    return `${Math.floor(duration / 1000)}秒`;
  } else {
    return `${Math.floor(duration / 60000)}分钟`;
  }
});

// 切换监控面板显示
function toggleMonitor() {
  showMonitor.value = !showMonitor.value;
  if (showMonitor.value) {
    refreshStats();
  }
}

// 刷新统计数据
function refreshStats() {
  try {
    const stats = getCacheStats();
    cacheStats.value = { ...stats };
  } catch (error) {
    console.error('获取缓存统计失败:', error);
  }
}

// 清空缓存
function clearCache() {
  try {
    clearCacheService();
    refreshStats();
    console.log('缓存已清空');
  } catch (error) {
    console.error('清空缓存失败:', error);
  }
}

// 定时刷新统计
let statsInterval = null;

onMounted(() => {
  // 每5秒刷新一次统计
  statsInterval = setInterval(() => {
    if (showMonitor.value) {
      refreshStats();
    }
  }, 5000);
});

onUnmounted(() => {
  if (statsInterval) {
    clearInterval(statsInterval);
  }
});
</script>

<style scoped>
.performance-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  font-size: 12px;
  backdrop-filter: blur(10px);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
  border-radius: 8px 8px 0 0;
}

.monitor-header h4 {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.monitor-content {
  padding: 15px;
}

.status-section,
.cache-section {
  margin-bottom: 15px;
}

.status-section h5,
.cache-section h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #555;
  border-bottom: 1px solid #eee;
  padding-bottom: 4px;
}

.status-item,
.cache-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.label {
  color: #666;
}

.value {
  color: #333;
  font-weight: 500;
}

.status.visible {
  color: #4CAF50;
  font-weight: 500;
}

.status.hidden {
  color: #FF5722;
  font-weight: 500;
}

.actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #f5f5f5;
}

.clear-btn {
  border-color: #FF5722;
  color: #FF5722;
}

.clear-btn:hover {
  background: #FF5722;
  color: white;
}

.refresh-btn {
  border-color: #2196F3;
  color: #2196F3;
}

.refresh-btn:hover {
  background: #2196F3;
  color: white;
}

.monitor-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #2196F3;
  color: white;
  border: none;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 999;
  transition: all 0.3s;
}

.monitor-toggle:hover {
  background: #1976D2;
  transform: scale(1.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .performance-monitor {
    width: 260px;
    right: 10px;
    top: 10px;
  }
  
  .monitor-toggle {
    bottom: 10px;
    right: 10px;
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
}
</style>
