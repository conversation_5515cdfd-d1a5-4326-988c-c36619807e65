import { ref, watch } from 'vue';
import { useServerStorage } from './serverStorage';
import { useSocket } from './socketService';

const STORAGE_KEY = 'vue_products_data';
const PRODUCT_NAMES_CACHE_KEY = 'vue_product_names_cache';

// 使用 ref 创建响应式商品列表
const products = ref([]);

// 商品名称缓存，用于保存已删除商品的名称
let productNamesCache = {};

// 获取服务器存储服务
const { getData, setData } = useServerStorage();

// 获取 WebSocket 服务
const { onDataUpdate, requestLatestData } = useSocket();

// 注册实时数据更新
let unsubscribe = null;

// 防止数据竞争的标志
let isUpdatingLocally = false;

// 处理产品数据更新
function handleProductDataUpdate(data) {
    // 如果正在本地更新，跳过服务器数据更新以避免冲突
    if (isUpdatingLocally) {
        return;
    }

    if (!data) {
        return;
    }

    try {
        const parsedData = typeof data === 'string' ? JSON.parse(data) : data;

        // 确保数据是数组且结构基本正确
        if (Array.isArray(parsedData)) {
            // 保留原有ID，确保与财务记录中的productId一致
            products.value = parsedData.map((p, index) => {
                // 如果没有ID（旧数据），则生成一个稳定的ID
                const stableId = p.id || `product-${index}-${p.name.replace(/\s+/g, '-').toLowerCase()}`;
                return {
                    id: stableId,
                    name: p.name || '',
                    price: parseFloat(p.price) || 0,
                    available: parseInt(p.available, 10) || 0,
                    remaining: parseInt(p.remaining, 10) ?? (parseInt(p.available, 10) || 0) // 兼容旧数据
                };
            });
        } else {
            console.warn("Invalid product data format received:", parsedData);
        }
    } catch (error) {
        console.error("Failed to process product data update:", error);
    }
}

// 加载商品名称缓存
function loadProductNamesCache() {
    try {
        const cachedData = localStorage.getItem(PRODUCT_NAMES_CACHE_KEY);
        if (cachedData) {
            productNamesCache = JSON.parse(cachedData);
            console.log("[ProductService] Loaded product names cache:", Object.keys(productNamesCache).length);
        }
    } catch (error) {
        console.error("[ProductService] Failed to load product names cache:", error);
        productNamesCache = {};
    }
}

// 保存商品名称缓存
function saveProductNamesCache() {
    try {
        localStorage.setItem(PRODUCT_NAMES_CACHE_KEY, JSON.stringify(productNamesCache));
        console.log("[ProductService] Saved product names cache:", Object.keys(productNamesCache).length);
    } catch (error) {
        console.error("[ProductService] Failed to save product names cache:", error);
    }
}

// 更新商品名称缓存
function updateProductNamesCache() {
    // 将当前所有商品的名称添加到缓存中
    products.value.forEach(product => {
        if (product.id && product.name) {
            productNamesCache[product.id] = product.name;
        }
    });

    // 保存更新后的缓存
    saveProductNamesCache();
}

// 获取商品名称（优先从商品列表获取，如果不存在则从缓存获取）
function getProductName(productId) {
    if (!productId) return '未知商品';

    // 首先从当前商品列表中查找
    const product = products.value.find(p => p.id === productId);
    if (product) return product.name;

    // 如果在当前列表中找不到，从缓存中查找
    if (productNamesCache[productId]) {
        return productNamesCache[productId];
    }

    // 如果缓存中也没有，返回未知商品
    return `未知商品 (ID: ${productId})`;
}

// --- 数据加载 --- //
async function loadProducts() {
    console.log("[ProductService] Starting to load products...");

    try {
        // 首先加载商品名称缓存
        loadProductNamesCache();

        console.log("[ProductService] Attempting to fetch data from server...");
        // 从服务器获取数据
        const data = await getData(STORAGE_KEY);
        console.log("[ProductService] Server response:", data);

        // 处理获取到的数据
        if (data) {
            console.log("[ProductService] Processing server data...");
            handleProductDataUpdate(data);
        } else {
            console.log("[ProductService] No product data found on server, checking localStorage...");

            // 尝试从本地存储加载（作为备份）
            try {
                const localData = localStorage.getItem(STORAGE_KEY);
                if (localData) {
                    console.log("[ProductService] Loading from localStorage as fallback");
                    handleProductDataUpdate(localData);
                } else {
                    console.log("[ProductService] No data in localStorage either, initializing empty list.");
                    products.value = [];
                }
            } catch (localError) {
                console.error("[ProductService] Failed to parse products from localStorage:", localError);
                products.value = [];
            }
        }

        // 注册实时数据更新
        if (unsubscribe) {
            unsubscribe(); // 取消之前的订阅
        }

        unsubscribe = onDataUpdate(STORAGE_KEY, handleProductDataUpdate);

        // 请求最新数据
        requestLatestData(STORAGE_KEY);

        // 更新商品名称缓存
        updateProductNamesCache();

        console.log("[ProductService] Product loading completed. Current products:", products.value.length);

    } catch (e) {
        console.error("[ProductService] Failed to load products from server:", e);

        // 尝试从本地存储加载（作为备份）
        try {
            const localData = localStorage.getItem(STORAGE_KEY);
            if (localData) {
                console.log("[ProductService] Loading from localStorage as fallback due to server error");
                handleProductDataUpdate(localData);
                // 更新商品名称缓存
                updateProductNamesCache();
            } else {
                console.log("[ProductService] No localStorage data available, initializing empty list");
                products.value = [];
            }
        } catch (localError) {
            console.error("[ProductService] Failed to parse products from localStorage (fallback):", localError);
            products.value = [];
        }
    }
}

// --- 数据保存 --- //
async function saveProducts() {
    try {
        // 保存所有数据，包括id，确保刷新后ID保持一致
        const dataToSave = products.value.map(p => ({
          id: p.id,
          name: p.name,
          price: p.price,
          available: p.available,
          remaining: p.remaining
        }));

        // 更新商品名称缓存
        updateProductNamesCache();

        // 保存到服务器
        const result = await setData(STORAGE_KEY, JSON.stringify(dataToSave));
        if (result.success) {
            console.log("Products saved to server");

            // 同时保存到本地存储作为备份
            localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
        } else {
            console.error("Failed to save products to server:", result.error);
            // 保存到本地存储作为备份
            localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
            console.log("Products saved to localStorage (fallback)");
        }
    } catch (e) {
        console.error("Failed to save products:", e);
        // 尝试保存到本地存储作为备份
        try {
            const dataToSave = products.value.map(p => ({
              id: p.id,
              name: p.name,
              price: p.price,
              available: p.available,
              remaining: p.remaining
            }));
            localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
            console.log("Products saved to localStorage (fallback)");

            // 即使保存失败，也尝试更新缓存
            updateProductNamesCache();
        } catch (localError) {
            console.error("Failed to save products to localStorage (fallback):", localError);
        }
    }
}

// --- 数据操作 --- //
function findProductIndex(productId) {
    return products.value.findIndex(p => p.id === productId);
}

// 本地更新辅助函数
async function performLocalUpdate(updateFunction) {
    isUpdatingLocally = true;
    try {
        const result = updateFunction();
        // 等待一小段时间确保更新完成
        await new Promise(resolve => setTimeout(resolve, 100));
        return result;
    } finally {
        isUpdatingLocally = false;
    }
}

function addProduct(productData) {
    const { name, price, available } = productData;
    if (!name) return { success: false, message: "商品名称不能为空！" };
    const priceNum = parseFloat(price);
    const availableNum = parseInt(available, 10);
    if (isNaN(priceNum) || priceNum < 0) return { success: false, message: "无效的价格输入！" };
    if (isNaN(availableNum) || availableNum < 0) return { success: false, message: "无效的在售数量输入！" };

    // 检查名称是否重复
    if (products.value.some(p => p.name === name)) {
        return { success: false, message: `商品 '${name}' 已存在！` };
    }

    // 使用更稳定的ID生成方式，基于名称和时间戳
    const timestamp = Date.now();
    const stableId = `product-${timestamp}-${name.trim().replace(/\s+/g, '-').toLowerCase()}`;
    const newProduct = {
        id: stableId,
        name: name.trim(),
        price: priceNum,
        available: availableNum,
        remaining: availableNum // 初始剩余数量等于在售数量
    };
    products.value.push(newProduct);
    return { success: true, product: newProduct };
}

function updateProduct(productId, updatedData) {
    const index = findProductIndex(productId);
    if (index === -1) return { success: false, message: "未找到要更新的商品！" };

    const { price, available, remaining } = updatedData;
    const priceNum = parseFloat(price);
    const availableNum = parseInt(available, 10);
    const remainingNum = parseInt(remaining, 10);

    if (isNaN(priceNum) || priceNum < 0) return { success: false, message: "无效的价格输入！" };
    if (isNaN(availableNum) || availableNum < 0) return { success: false, message: "无效的在售数量输入！" };
    if (isNaN(remainingNum) || remainingNum < 0) return { success: false, message: "无效的剩余数量输入！" };
    if (remainingNum > availableNum) return { success: false, message: "剩余数量不能超过在售数量！" };

    // 创建一个新对象来触发响应式更新
    products.value[index] = {
        ...products.value[index], // 保留 id 和 name
        price: priceNum,
        available: availableNum,
        remaining: remainingNum
    };
    return { success: true, product: products.value[index] };
}

function deleteProduct(productId) {
    const index = findProductIndex(productId);
    if (index === -1) return { success: false, message: "未找到要删除的商品！" };

    const deletedProduct = products.value.splice(index, 1)[0];
    return { success: true, product: deletedProduct };
}

async function sellProduct(productId) {
    return await performLocalUpdate(() => {
        const index = findProductIndex(productId);
        if (index === -1) return { success: false, message: "未找到商品！" };

        const product = products.value[index];
        if (product.remaining > 0) {
            const newRemaining = product.remaining - 1;
            // 创建新对象以确保响应式更新
            products.value[index] = {
                ...product,
                remaining: newRemaining
            };

            if (newRemaining === 0) {
                return { success: true, message: `商品 '${product.name}' 卖完了！需要尽快补货！！` };
            }
            return { success: true };
        } else {
            return { success: false, message: `商品 '${product.name}' 当前已售罄！` };
        }
    });
}

// --- 新增：补货功能 --- //
async function restockProduct(productId) {
    return await performLocalUpdate(() => {
        const index = findProductIndex(productId);
        if (index === -1) return { success: false, message: "未找到商品！" };

        const product = products.value[index];
        if (product.remaining === product.available) {
            return { success: true, message: `商品 '${product.name}' 库存已满，无需补货。`, noAction: true };
        }

        // 创建新对象以确保响应式更新
        products.value[index] = {
            ...product,
            remaining: product.available
        };

        return { success: true, message: `商品 '${product.name}' 已成功补货！` };
    });
}
// --- 结束：补货功能 --- //

// --- 新增：增加库存功能 ---
async function increaseStock(productId, quantity = 1) {
    return await performLocalUpdate(() => {
        const index = findProductIndex(productId);
        if (index === -1) return { success: false, message: "未找到要增加库存的商品！" };

        const product = products.value[index];
        // 增加库存不能超过"在售数量"（如果需要此限制）
        const newRemaining = product.remaining + quantity;
        if (newRemaining > product.available) {
            return { success: false, message: `增加后的库存 (${newRemaining}) 不能超过在售数量 (${product.available})` };
        }

        // 创建新对象以确保响应式更新
        products.value[index] = {
            ...product,
            remaining: newRemaining
        };

        return { success: true };
    });
}
// --- 结束：增加库存功能 ---

// --- 修改：重置商品列表（完全清空，但保留商品名称缓存） --- //
function resetProducts() {
    // 确保商品名称缓存已加载
    loadProductNamesCache();

    // 在重置前，先将所有商品的名称保存到缓存中
    products.value.forEach(product => {
        if (product.id && product.name) {
            productNamesCache[product.id] = product.name;
        }
    });

    // 保存更新后的缓存
    saveProductNamesCache();

    // 记录重置前的商品数量
    const originalCount = products.value.length;

    // 完全清空商品列表
    products.value = [];

    console.log("[ProductService] Products reset. All products cleared.");
    console.log("[ProductService] Product names cache size:", Object.keys(productNamesCache).length);

    return {
        success: true,
        message: `商品列表已重置！已清空所有${originalCount}个商品。所有商品名称已缓存，账单记录中的商品名称将保持不变。`
    };
}
// --- 结束：修改重置商品列表 --- //

// --- 初始化和监听 --- //
// 加载初始数据
loadProducts();

// 监听 products 数组的变化，并在变化时自动保存
// 使用 deep: true 来监听对象内部属性的变化
watch(products, saveProducts, { deep: true });

// --- 导出 --- //
// 导出响应式引用和操作函数
export const useProducts = () => {
    return {
        products,
        loadProducts,
        saveProducts,
        addProduct,
        updateProduct,
        deleteProduct,
        sellProduct,
        restockProduct,
        increaseStock,
        resetProducts,
        getProductName
        // 批量导入函数可以后续添加
    };
};