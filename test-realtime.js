// 测试实时更新功能
import { io } from 'socket.io-client';

console.log('🔗 连接到 WebSocket 服务器...');
const socket = io('http://localhost:3000');

socket.on('connect', () => {
    console.log('✅ WebSocket 连接成功');
    
    // 请求所有数据
    console.log('📡 请求所有数据...');
    socket.emit('request-data');
});

socket.on('data-refresh', (data) => {
    console.log('📥 收到数据更新:', Object.keys(data));
});

socket.on('data-updated', (data) => {
    console.log('🔄 收到数据更新通知:', data);
});

socket.on('disconnect', () => {
    console.log('❌ WebSocket 连接断开');
});

socket.on('error', (error) => {
    console.error('❌ WebSocket 错误:', error);
});

// 保持脚本运行
console.log('⏳ 等待数据更新...');
