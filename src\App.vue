<template>
  <div id="app-layout">
    <nav>
      <router-link to="/">商品管理</router-link> |
      <router-link to="/finance">商单登记</router-link> |
      <router-link to="/history">查看账单</router-link>
      <connection-status />
    </nav>
    <main>
      <router-view /> <!-- 路由对应的组件将在这里渲染 -->
    </main>
    <footer>
      <!-- 页脚内容（可选） -->
      <p class="version-info">版本: 1.0.2 - 实时同步版</p>
    </footer>
  </div>
</template>

<script setup>
import ConnectionStatus from './components/ConnectionStatus.vue';
</script>

<style scoped>
#app-layout {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 10px;
  box-sizing: border-box;
}

nav {
  padding: 15px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  text-align: center;
}

nav a {
  font-weight: bold;
  color: #2c3e50;
  text-decoration: none;
  padding: 0 10px;
}

nav a.router-link-exact-active {
  color: #42b983;
}

main {
  margin-top: 20px;
}

footer {
  margin-top: 30px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  text-align: center;
  font-size: 0.8em;
  color: #666;
}

.version-info {
  margin: 0;
}

/* 将之前的全局模态框样式移到这里 */
:global(.modal-overlay) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

:global(.modal-content) {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
  min-width: 300px;
  max-width: 500px;
    position: relative; /* 添加这行以便关闭按钮定位 */
}

:global(.modal-close-button) {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #aaa;
}
:global(.modal-close-button:hover) {
    color: #333;
}

/* 响应式设计 */
@media (min-width: 768px) {
  #app-layout {
    padding: 15px;
    max-width: 90%;
  }
}

@media (min-width: 1200px) {
  #app-layout {
    max-width: 1200px;
    padding: 20px;
  }
}

@media (max-width: 767px) {
  nav {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px 0;
  }

  nav a {
    display: block;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
  }

  nav a:last-child {
    border-bottom: none;
  }
}

</style>