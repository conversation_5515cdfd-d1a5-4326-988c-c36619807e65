<template>
  <div class="history-container">

    <!-- 日期选择区域 -->
    <div class="date-selector">
      <div class="date-mode-selector">
        <div class="date-mode-flex-container">
          <div class="mode-selector-row">
            <label class="mode-label">查询模式：</label>
            <div class="mode-options">
              <label class="mode-option">
                <input type="radio" v-model="dateMode" value="single" @change="handleDateModeChange" />
                <span>单日查询</span>
              </label>
              <label class="mode-option">
                <input type="radio" v-model="dateMode" value="range" @change="handleDateModeChange" />
                <span>时间段查询</span>
              </label>
            </div>
          </div>

          <!-- 销售热力图 -->
          <div class="heatmap-container">
            <div class="heatmap-header">
              <div class="month-title" @click="openMonthPicker">
                {{ selectedYear }}年{{ selectedMonth + 1 }}月 ▾
              </div>

              <!-- 年月选择器 -->
              <div v-if="showMonthPicker" class="month-picker">
                <div class="year-selector">
                  <button @click="changeYear(-1)" class="year-nav-btn">◀</button>
                  <span>{{ selectedYear }}年</span>
                  <button @click="changeYear(1)" class="year-nav-btn">▶</button>
                </div>
                <div class="months-grid">
                  <div
                    v-for="(name, index) in ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']"
                    :key="index"
                    class="month-item"
                    :class="{ 'active': index === selectedMonth }"
                    @click="selectMonth(index)"
                  >
                    {{ name }}
                  </div>
                </div>
              </div>
            </div>
            <div class="calendar-heatmap">
              <div class="weekday-header">
                <div class="weekday">一</div>
                <div class="weekday">二</div>
                <div class="weekday">三</div>
                <div class="weekday">四</div>
                <div class="weekday">五</div>
                <div class="weekday">六</div>
                <div class="weekday">日</div>
              </div>
              <div class="calendar-grid">
                <template v-for="(week, weekIndex) in calendarData" :key="weekIndex">
                  <div class="calendar-week">
                    <template v-for="(day, dayIndex) in week" :key="`${weekIndex}-${dayIndex}`">
                      <div
                        class="calendar-day"
                        :class="{
                          'empty-day': !day.day,
                          'has-sales': day.count > 0
                        }"
                        :style="day.percentage > 0 ? { backgroundColor: getColorByPercentage(day.percentage) } : {}"
                        @click="day.date && selectDate(day.date)"
                      >
                        <span v-if="day.day" class="day-number">{{ day.day }}</span>
                      </div>
                    </template>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 单日查询模式 -->
      <div v-if="dateMode === 'single'" class="single-date-selector">
        <label for="history-date">选择日期：</label>
        <input
          type="date"
          id="history-date"
          v-model="selectedDate"
          :max="today"
          @change="handleDateChange"
          ref="dateInput"
        />
      </div>

      <!-- 时间段查询模式 -->
      <div v-else class="date-range-selector">
        <div class="date-range-input">
          <label for="start-date">开始日期：</label>
          <input
            type="date"
            id="start-date"
            v-model="startDate"
            :max="endDate || today"
            @change="handleDateRangeChange"
            ref="startDateInput"
          />
        </div>
        <div class="date-range-input">
          <label for="end-date">结束日期：</label>
          <input
            type="date"
            id="end-date"
            v-model="endDate"
            :min="startDate"
            :max="today"
            @change="handleDateRangeChange"
            ref="endDateInput"
          />
        </div>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-if="noDataForDate" class="no-data-message">
      <p v-if="dateMode === 'single'">所选日期没有销售记录</p>
      <p v-else>所选时间段内没有销售记录</p>
    </div>

    <!-- 销售流水记录表格 -->
    <div v-if="!noDataForDate" class="history-records">
      <h3>{{ dateRangeTitle }} 账单记录</h3>
      <table id="history-finance-table">
        <thead>
          <tr>
            <th>日期</th>
            <th>时间</th>
            <th>商品</th>
            <th>售货员</th>
            <th>金额</th>
            <th class="rebate-header">
              返利({{ (getRebateRate(selectedDate) * 100).toFixed(0) }}%)
              <button @click="showRebateEditor = true" class="edit-rebate-btn" title="修改返利系数">
                <span class="edit-icon">✎</span>
              </button>
            </th>
            <th>利润</th>
            <th>备注</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(record, index) in filteredRecords" :key="index">
            <td>{{ record.date }}</td>
            <td>{{ record.time || '-' }}</td>
            <td>{{ getProductName(record.productId) }}</td>
            <td>
              <!-- 使用 el-tag 组件 -->
              <el-tag
                v-if="record.salesperson"
                :type="getTagType(record.salesperson)"
                effect="light"
                round>
                {{ record.salesperson }}
              </el-tag>
              <span v-else>-</span>
            </td>
            <td>¥{{ record.saleAmount.toFixed(2) }}</td>
            <td>{{ calculateRebate(record) > 0 ? '¥' + calculateRebate(record).toFixed(2) : '-' }}</td>
            <td>¥{{ calculateProfit(record).toFixed(2) }}</td>
            <td class="notes-cell" :title="record.notes">{{ record.notes || '-' }}</td>
            <td class="actions-cell">
              <button @click="openEditModal(record)" class="edit-button">编辑</button>
              <button @click="deleteRecord(record.id)" class="delete-button">删除</button>
            </td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="4" class="total-label">总计</td>
            <td>¥{{ totalAmount.toFixed(2) }}</td>
            <td>¥{{ totalRebate.toFixed(2) }}</td>
            <td>¥{{ totalProfit.toFixed(2) }}</td>
            <td colspan="2"></td>
          </tr>
        </tfoot>
      </table>
    </div>

    <!-- 当日总结 -->
    <div v-if="hasDailySummary" class="history-summary">
      <div class="summary-header">
        <div class="summary-title">
          <i class="summary-icon">📊</i>
          <h3>{{ dateRangeTitle }} {{ dateMode === 'single' ? '日' : '' }}结账总结</h3>
        </div>
      </div>
      <div class="summary-content">
        <div class="summary-main">
          <div class="summary-stat">
            <div class="stat-label">总流水</div>
            <div class="stat-value revenue">¥{{ dailySummary.totalRevenue.toFixed(2) }}</div>
          </div>
          <div class="summary-stat">
            <div class="stat-label">总利润</div>
            <div class="stat-value profit">¥{{ dailySummary.totalProfit.toFixed(2) }}</div>
          </div>
          <div class="summary-stat">
            <div class="stat-label">销售冠军</div>
            <div class="stat-value champion">
              <span class="champion-name">{{ dailySummary.topSalesperson || '无' }}</span>
              <span class="champion-amount" v-if="dailySummary.topSalesperson">
                ¥{{ dailySummary.topSalespersonAmount.toFixed(2) }}
              </span>
            </div>
          </div>
        </div>
        <div class="summary-details">
          <div class="summary-section product-sales">
            <h4><i class="section-icon">🛒</i> 商品销售情况</h4>
            <ul class="product-list">
              <li
                v-for="item in sortedProductSales"
                :key="item.name"
                class="product-item"
                @click="openProductDetails(item.name)"
                :title="'点击查看' + item.name + '的详细销售记录'"
              >
                <div class="product-name">
                  {{ item.name }}

                </div>
                <div class="product-stats">
                  <span class="product-quantity">{{ item.quantity }} 件</span>
                  <span class="product-amount">¥{{ item.amount.toFixed(2) }}</span>
                </div>
              </li>
            </ul>
          </div>
          <div class="summary-section salesperson-rebates">
            <h4><i class="section-icon">👨‍💼</i> 销售员返利</h4>
            <ul class="rebate-list">
              <li v-for="item in sortedSalespersonRebates" :key="item.name" class="rebate-item">
                <div class="salesperson-name">
                  <el-tag
                    :type="getTagType(item.name)"
                    effect="light"
                    size="small"
                    round>
                    {{ item.name }}
                  </el-tag>
                </div>
                <div class="rebate-amount">¥{{ item.rebate.toFixed(2) }}</div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>



    <!-- 图表分析区域 -->
    <div v-if="!noDataForDate" class="charts-container">
      <h3>数据分析图表</h3>
      <div class="charts-grid">
        <div class="chart-box">
          <h4>销售分布分析</h4>
          <div ref="salesChart" class="chart"></div>
        </div>
        <div class="chart-box">
          <h4>利润与返利分析</h4>
          <div ref="profitChart" class="chart"></div>
        </div>
      </div>
    </div>

    <!-- 导出按钮区域 -->
    <div v-if="!noDataForDate" class="export-buttons">
      <button @click="exportFullPageImage" class="export-btn primary-btn">
        <i class="export-icon">📷</i> 导出完整图片
      </button>
      <button @click="exportHistoryToExcel" class="export-btn">
        <i class="export-icon">📊</i> 导出 Excel
      </button>
      <button @click="backupAllData" class="export-btn backup-btn">
        <i class="export-icon">💾</i> 备份所有数据
      </button>
    </div>

    <!-- 返利系数编辑器弹窗 -->
    <div v-if="showRebateEditor" class="rebate-editor-modal">
      <div class="rebate-editor-content">
        <h3>修改返利系数</h3>
        <p class="rebate-date-info" v-if="dateMode === 'single'">日期: {{ selectedDate }}</p>
        <p class="rebate-date-info" v-else>注意: 在时间段模式下，返利系数修改仅应用于当前选择的日期 ({{ selectedDate }})</p>
        <div class="rebate-editor-form">
          <div class="rate-input-group modal-input-group">
            <input
              type="number"
              v-model="rebateRatePercent"
              min="1"
              max="100"
              step="1"
              ref="rebateInput"
            />
            <span class="percent-sign">%</span>
          </div>
          <div class="rebate-editor-buttons">
            <button @click="updateRebateRateSetting" class="save-rebate-btn">保存</button>
            <button @click="showRebateEditor = false" class="cancel-rebate-btn">取消</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <edit-finance-record-modal
      v-if="editingRecord"
      :record="editingRecord"
      @close="closeEditModal"
      @save="handleSaveEdit"
    />

    <!-- 商品详情弹窗 -->
    <div v-if="showProductDetails" class="product-details-modal">
      <div class="product-details-content">
        <div class="product-details-header">
          <h3>{{ selectedProduct }} 销售详情</h3>
          <button @click="closeProductDetails" class="close-button">×</button>
        </div>

        <div class="product-details-summary">
          <div class="detail-stat">
            <div class="stat-label">总销量</div>
            <div class="stat-value">{{ productRecords.length }} 件</div>
          </div>
          <div class="detail-stat">
            <div class="stat-label">总金额</div>
            <div class="stat-value revenue">¥{{ productRecords.reduce((sum, record) => sum + record.saleAmount, 0).toFixed(2) }}</div>
          </div>
        </div>

        <div class="product-details-records">
          <table class="product-records-table">
            <thead>
              <tr>
                <th>日期</th>
                <th>时间</th>
                <th>售货员</th>
                <th>金额</th>
                <th>备注</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(record, index) in productRecords" :key="index">
                <td>{{ record.date }}</td>
                <td>{{ record.time || '-' }}</td>
                <td>
                  <el-tag
                    v-if="record.salesperson"
                    :type="getTagType(record.salesperson)"
                    effect="light"
                    size="small"
                    round>
                    {{ record.salesperson }}
                  </el-tag>
                  <span v-else>-</span>
                </td>
                <td>¥{{ record.saleAmount.toFixed(2) }}</td>
                <td class="notes-cell" :title="record.notes">{{ record.notes || '-' }}</td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="3" class="total-label">总计</td>
                <td>¥{{ productRecords.reduce((sum, record) => sum + record.saleAmount, 0).toFixed(2) }}</td>
                <td></td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    <!-- 时间段销售详情弹窗 -->
    <div v-if="showTimeDetails" class="time-details-modal">
      <div class="time-details-content">
        <div class="time-details-header">
          <h3 v-if="dateMode === 'single'">{{ selectedDate }} {{ selectedTimeSlot }}:00-{{ selectedTimeSlot }}:59 销售详情</h3>
          <h3 v-else>{{ selectedTimeSlot }} 销售详情</h3>
          <button @click="closeTimeSlotDetails" class="close-button">×</button>
        </div>

        <div class="time-details-summary">
          <div class="detail-stat">
            <div class="stat-label">销售件数</div>
            <div class="stat-value">{{ timeSlotRecords.length }} 件</div>
          </div>
          <div class="detail-stat">
            <div class="stat-label">总金额</div>
            <div class="stat-value revenue">¥{{ timeSlotRecords.reduce((sum, record) => sum + record.saleAmount, 0).toFixed(2) }}</div>
          </div>
          <div class="detail-stat">
            <div class="stat-label">平均金额</div>
            <div class="stat-value">¥{{ timeSlotRecords.length > 0 ? (timeSlotRecords.reduce((sum, record) => sum + record.saleAmount, 0) / timeSlotRecords.length).toFixed(2) : '0.00' }}</div>
          </div>
        </div>

        <div class="time-details-records">
          <table class="time-records-table">
            <thead>
              <tr>
                <th v-if="dateMode !== 'single'">日期</th>
                <th>时间</th>
                <th>商品</th>
                <th>售货员</th>
                <th>金额</th>
                <th>备注</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(record, index) in timeSlotRecords" :key="index">
                <td v-if="dateMode !== 'single'">{{ record.date }}</td>
                <td>{{ record.time || '-' }}</td>
                <td>{{ getProductName(record.productId) }}</td>
                <td>
                  <el-tag
                    v-if="record.salesperson"
                    :type="getTagType(record.salesperson)"
                    effect="light"
                    size="small"
                    round>
                    {{ record.salesperson }}
                  </el-tag>
                  <span v-else>-</span>
                </td>
                <td>¥{{ record.saleAmount.toFixed(2) }}</td>
                <td class="notes-cell" :title="record.notes">{{ record.notes || '-' }}</td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td :colspan="dateMode === 'single' ? 3 : 4" class="total-label">总计</td>
                <td>¥{{ timeSlotRecords.reduce((sum, record) => sum + record.saleAmount, 0).toFixed(2) }}</td>
                <td></td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    <!-- 反馈消息 -->
    <div v-if="feedbackMessage" class="feedback" :class="feedbackType">
      {{ feedbackMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useFinance } from '../services/financeService';
import { useProducts } from '../services/productService';
import { useSettings } from '../services/settingsService';
import { useServerStorage } from '../services/serverStorage';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';
import { ElTag } from 'element-plus';
import EditFinanceRecordModal from '../components/EditFinanceRecordModal.vue';
import * as echarts from 'echarts/core';
import {
  BarChart,
  LineChart,
  PieChart
} from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent
} from 'echarts/components';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  BarChart,
  LineChart,
  PieChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer
]);

// 获取财务和商品服务
const { financialRecords, updateFinancialRecord, deleteFinancialRecord } = useFinance();
const { products, getProductName } = useProducts();

// 获取服务器存储服务
const { backupData } = useServerStorage();

// 编辑相关状态
const editingRecord = ref(null);

// 商品详情弹窗状态
const showProductDetails = ref(false);
const selectedProduct = ref(null);
const productRecords = ref([]);

// 时间段销售详情弹窗状态
const showTimeDetails = ref(false);
const selectedTimeSlot = ref(null);
const timeSlotRecords = ref([]);

// 销售热力图状态
const selectedMonth = ref(new Date().getMonth()); // 默认当前月份
const selectedYear = ref(new Date().getFullYear()); // 默认当前年份
const showMonthPicker = ref(false);

// 反馈消息状态
const feedbackMessage = ref('');
const feedbackType = ref('info'); // 'info', 'success', 'error'
let feedbackTimeout = null;

// 日期相关函数，使用北京时间（东八区）
function getCurrentDate() {
  // 获取当前日期（使用北京时间/东八区）
  const now = new Date();
  // 获取年、月、日（北京时间）
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要+1
  const day = String(now.getDate()).padStart(2, '0');
  // 返回格式化的日期字符串 YYYY-MM-DD
  return `${year}-${month}-${day}`;
}

// 使用函数获取当前日期，确保每次使用时都是最新的
const dateMode = ref('single'); // 'single' 或 'range'
const selectedDate = ref(getCurrentDate());
const startDate = ref(getCurrentDate());
const endDate = ref(getCurrentDate());

// 更新当前日期的函数
function updateCurrentDate() {
  const newDate = getCurrentDate();

  // 只有当日期真的变化时才更新
  if (selectedDate.value !== newDate && !isDateManuallySelected) {
    console.log('日期已更新为:', newDate);
    selectedDate.value = newDate;
    startDate.value = newDate;
    endDate.value = newDate;

    // 如果当前是在查看今天的数据，则重新加载数据
    loadHistoryData();
  }
}

// 标记用户是否手动选择了日期
let isDateManuallySelected = false;

// 格式化日期显示
const formattedDate = computed(() => {
  const date = new Date(selectedDate.value);
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' });
});

// 格式化日期范围显示
const formattedStartDate = computed(() => {
  const date = new Date(startDate.value);
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' });
});

const formattedEndDate = computed(() => {
  const date = new Date(endDate.value);
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' });
});

// 日期范围标题
const dateRangeTitle = computed(() => {
  if (dateMode.value === 'single') {
    return formattedDate.value;
  } else {
    if (startDate.value === endDate.value) {
      return formattedStartDate.value;
    } else {
      return `${formattedStartDate.value} 至 ${formattedEndDate.value}`;
    }
  }
});

// 获取设置服务
const { getRebateRate, updateRebateRate } = useSettings();

// 返利系数编辑器状态
const showRebateEditor = ref(false);
const rebateRatePercent = ref(5); // 默认值，将在打开弹窗时更新

// 筛选当天的记录
const filteredRecords = ref([]);
const dailySummary = ref(null);
const noDataForDate = ref(false);
const hasDailySummary = computed(() => dailySummary.value !== null);

// 计算总金额
const totalAmount = computed(() => {
  return filteredRecords.value.reduce((sum, record) => sum + record.saleAmount, 0);
});

// 计算总返利
const totalRebate = computed(() => {
  return filteredRecords.value.reduce((sum, record) => sum + calculateRebate(record), 0);
});

// 计算总利润
const totalProfit = computed(() => {
  return filteredRecords.value.reduce((sum, record) => sum + calculateProfit(record), 0);
});

// 按销售量从高到低排序的商品销售情况
const sortedProductSales = computed(() => {
  if (!dailySummary.value || !dailySummary.value.productSales) return [];

  // 将对象转换为数组，并添加名称属性
  const productsArray = Object.entries(dailySummary.value.productSales).map(([name, data]) => ({
    name,
    quantity: data.quantity,
    amount: data.amount
  }));

  // 按销售量从高到低排序
  return productsArray.sort((a, b) => b.quantity - a.quantity);
});

// 按返利金额从高到低排序的销售员返利
const sortedSalespersonRebates = computed(() => {
  if (!dailySummary.value || !dailySummary.value.salespersonRebates) return [];

  // 将对象转换为数组，并添加名称属性
  const rebatesArray = Object.entries(dailySummary.value.salespersonRebates).map(([name, rebate]) => ({
    name,
    rebate
  }));

  // 按返利金额从高到低排序
  return rebatesArray.sort((a, b) => b.rebate - a.rebate);
});

// 计算单条记录的返利
function calculateRebate(record) {
  if (!record.salesperson) return 0;
  // 使用记录日期的返利比例
  const recordRebateRate = getRebateRate(record.date);
  // 使用四舍五入，与财务登记页面保持一致
  return Math.round(record.saleAmount * recordRebateRate);
}

// 计算单条记录的利润
function calculateProfit(record) {
  // 利润 = 销售额 - 返利
  const rebate = calculateRebate(record);
  return record.saleAmount - rebate;
}

// 获取标签类型
// 与 Finance.vue 保持一致的售货员标签颜色
const tagTypes = ['success', 'info', 'warning']; // 其他售货员的备选类型
const salespersonTagTypeCache = {}; // 缓存售货员名称与颜色的映射
let otherTypeIndex = 0; // 用于其他售货员的索引

function getTagType(name) {
  if (!name) return '';

  // 固定颜色
  if (name === '李洁涵') {
    return 'danger'; // 红色
  }
  if (name === '刘昕怡') {
    return ''; // 蓝色 (默认主题色)
  }

  // 对于其他售货员，从备选类型中轮流选择
  if (salespersonTagTypeCache[name]) {
    return salespersonTagTypeCache[name];
  }
  const type = tagTypes[otherTypeIndex % tagTypes.length];
  salespersonTagTypeCache[name] = type;
  otherTypeIndex++;
  return type;
}

// 处理日期模式变更
function handleDateModeChange() {
  if (dateMode.value === 'single') {
    // 切换到单日模式时，使用当前选择的日期
    loadHistoryData();
  } else {
    // 切换到时间段模式时，默认使用当前日期作为开始和结束日期
    handleDateRangeChange();
  }
}

// 处理单日日期变更
async function handleDateChange() {
  // 标记用户已手动选择日期
  isDateManuallySelected = selectedDate.value !== getCurrentDate();

  // 加载选定日期的数据
  await loadHistoryData();

  // 关闭日期选择器
  // 在移动设备上，这可能不会生效，因为移动浏览器的日期选择器行为不同
  if (document.activeElement === document.getElementById('history-date')) {
    document.activeElement.blur();
  }
}

// 处理日期范围变更
async function handleDateRangeChange() {
  // 确保开始日期不晚于结束日期
  if (startDate.value > endDate.value) {
    endDate.value = startDate.value;
  }

  // 标记用户已手动选择日期
  isDateManuallySelected = startDate.value !== getCurrentDate() || endDate.value !== getCurrentDate();

  // 加载日期范围内的数据
  await loadHistoryData();

  // 关闭日期选择器
  const activeElement = document.activeElement;
  if (activeElement === document.getElementById('start-date') ||
      activeElement === document.getElementById('end-date')) {
    activeElement.blur();
  }
}

// 加载历史数据
function loadHistoryData() {
  return new Promise((resolve) => {
    if (dateMode.value === 'single') {
      // 单日模式：筛选选定日期的记录
      filteredRecords.value = financialRecords.value.filter(record => record.date === selectedDate.value);
    } else {
      // 时间段模式：筛选日期范围内的记录
      filteredRecords.value = financialRecords.value.filter(record => {
        return record.date >= startDate.value && record.date <= endDate.value;
      });

      // 按日期排序
      filteredRecords.value.sort((a, b) => {
        // 首先按日期排序
        if (a.date !== b.date) {
          return a.date.localeCompare(b.date);
        }
        // 如果日期相同，按时间排序（如果有时间）
        if (a.time && b.time) {
          return a.time.localeCompare(b.time);
        }
        return 0;
      });
    }

    // 检查是否有数据
    if (filteredRecords.value.length === 0) {
      noDataForDate.value = true;
      dailySummary.value = null;
      resolve();
      return;
    }

    noDataForDate.value = false;

    // 生成总结
    generateDailySummary();

    // 初始化图表
    initCharts();

    // 更新热力图
    updateCalendarLogging();

    // 确保所有数据处理完成
    nextTick(() => {
      resolve();
    });
  });
}

// 生成总结
function generateDailySummary() {
  // 初始化总结数据
  const summary = {
    date: dateRangeTitle.value,
    totalRevenue: 0,
    totalProfit: 0,
    topSalesperson: null,
    topSalespersonAmount: 0,
    productSales: {},
    salespersonRebates: {}
  };

  // 销售员销售额统计
  const salespersonSales = {};

  // 日期统计（用于时间段模式）
  const dateStats = {};

  // 处理每条记录
  filteredRecords.value.forEach(record => {
    // 累计总收入
    summary.totalRevenue += record.saleAmount;

    // 计算利润 (销售额 - 返利)
    const rebate = calculateRebate(record);
    const profit = record.saleAmount - rebate;
    summary.totalProfit += profit;

    // 查找商品信息
    const product = products.value.find(p => p.id === record.productId);
    if (product) {
      const productName = product.name;

      // 更新商品销售情况
      if (!summary.productSales[productName]) {
        summary.productSales[productName] = { quantity: 0, amount: 0 };
      }
      summary.productSales[productName].quantity += 1;
      summary.productSales[productName].amount += record.saleAmount;
    }

    // 更新销售员销售额
    if (record.salesperson) {
      if (!salespersonSales[record.salesperson]) {
        salespersonSales[record.salesperson] = 0;
      }
      salespersonSales[record.salesperson] += record.saleAmount;
    }

    // 在时间段模式下，统计每天的销售情况
    if (dateMode.value === 'range') {
      if (!dateStats[record.date]) {
        dateStats[record.date] = {
          sales: 0,
          count: 0
        };
      }
      dateStats[record.date].sales += record.saleAmount;
      dateStats[record.date].count += 1;
    }
  });

  // 找出销售冠军
  let maxSales = 0;
  for (const [name, amount] of Object.entries(salespersonSales)) {
    if (amount > maxSales) {
      maxSales = amount;
      summary.topSalesperson = name;
      summary.topSalespersonAmount = amount;
    }

    // 计算返利
    if (dateMode.value === 'single') {
      // 单日模式：使用选定日期的返利比例
      const dateRebateRate = getRebateRate(selectedDate.value);
      summary.salespersonRebates[name] = Math.round(amount * dateRebateRate);
    } else {
      // 时间段模式：需要按每条记录的日期分别计算返利
      let totalRebate = 0;

      // 遍历该销售员的所有记录，按日期分别计算返利
      filteredRecords.value.forEach(record => {
        if (record.salesperson === name) {
          const recordRebateRate = getRebateRate(record.date);
          totalRebate += Math.round(record.saleAmount * recordRebateRate);
        }
      });

      summary.salespersonRebates[name] = totalRebate;
    }
  }

  // 保存日期统计数据，用于图表展示
  if (dateMode.value === 'range') {
    summary.dateStats = dateStats;
  }

  dailySummary.value = summary;
}

// 打开商品详情弹窗
function openProductDetails(productName) {
  // 设置选中的商品
  selectedProduct.value = productName;

  // 筛选该商品的所有销售记录
  // 首先找到商品的ID
  const product = products.value.find(p => p.name === productName);

  if (product) {
    // 使用商品ID筛选记录
    productRecords.value = filteredRecords.value.filter(record =>
      record.productId === product.id
    );

    // 按日期和时间排序
    productRecords.value.sort((a, b) => {
      // 首先按日期排序
      if (a.date !== b.date) {
        return a.date.localeCompare(b.date);
      }
      // 如果日期相同，按时间排序（如果有时间）
      if (a.time && b.time) {
        return a.time.localeCompare(b.time);
      }
      return 0;
    });

    // 显示弹窗
    showProductDetails.value = true;
  } else {
    // 如果找不到商品，可能是使用了缓存的商品名称
    // 直接使用商品名称筛选记录
    productRecords.value = filteredRecords.value.filter(record =>
      getProductName(record.productId) === productName
    );

    // 按日期和时间排序
    productRecords.value.sort((a, b) => {
      // 首先按日期排序
      if (a.date !== b.date) {
        return a.date.localeCompare(b.date);
      }
      // 如果日期相同，按时间排序（如果有时间）
      if (a.time && b.time) {
        return a.time.localeCompare(b.time);
      }
      return 0;
    });

    // 显示弹窗
    showProductDetails.value = true;
  }
}

// 关闭商品详情弹窗
function closeProductDetails() {
  showProductDetails.value = false;
  selectedProduct.value = null;
  productRecords.value = [];
}

// 生成热力图数据
const calendarData = computed(() => {
  // 获取所选月份的所有记录
  const year = selectedYear.value;
  const month = selectedMonth.value;

  // 获取该月的第一天和最后一天
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);

  // 格式化日期为 YYYY-MM-DD 格式
  const formatDate = (date) => {
    const y = date.getFullYear();
    const m = String(date.getMonth() + 1).padStart(2, '0');
    const d = String(date.getDate()).padStart(2, '0');
    return `${y}-${m}-${d}`;
  };

  const firstDayStr = formatDate(firstDay);
  const lastDayStr = formatDate(lastDay);

  // 筛选该月的所有记录
  // 确保 financialRecords.value 存在且是数组
  const monthRecords = (financialRecords.value || []).filter(record => {
    return record.date >= firstDayStr && record.date <= lastDayStr;
  });

  // 按日期统计销售额
  const salesByDate = {};

  // 初始化该月所有日期的销售额为0
  for (let d = new Date(firstDay); d <= lastDay; d.setDate(d.getDate() + 1)) {
    salesByDate[formatDate(d)] = {
      amount: 0,
      count: 0
    };
  }

  // 统计每天的销售额和销售件数
  monthRecords.forEach(record => {
    if (salesByDate[record.date] !== undefined) {
      salesByDate[record.date].amount += record.saleAmount || 0;
      salesByDate[record.date].count++;
    }
  });

  // 计算月总销售额
  let totalMonthSales = 0;
  Object.values(salesByDate).forEach(data => {
    totalMonthSales += data.amount;
  });

  // 计算每天销售额占比
  Object.keys(salesByDate).forEach(date => {
    const percentage = totalMonthSales > 0
      ? (salesByDate[date].amount / totalMonthSales) * 100
      : 0;
    salesByDate[date].percentage = percentage;
  });

  // 计算该月的日历数据
  // 获取该月第一天是星期几（0-6，0是星期日）
  let firstDayOfWeek = firstDay.getDay();
  // 调整为周一为一周的第一天 (0 = 周一, 6 = 周日)
  firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

  // 获取该月的总天数
  const daysInMonth = lastDay.getDate();

  // 创建日历数据
  const result = [];

  // 添加日历数据
  // 第一行可能有空白
  let week = [];

  // 添加第一行前面的空白
  for (let i = 0; i < firstDayOfWeek; i++) {
    week.push({
      day: null,
      count: 0,
      amount: 0,
      percentage: 0,
      date: null
    });
  }

  // 添加日期
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day);
    const dateStr = formatDate(date);
    const data = salesByDate[dateStr] || { count: 0, amount: 0, percentage: 0 };

    week.push({
      day,
      count: data.count,
      amount: data.amount,
      percentage: data.percentage,
      date: dateStr
    });

    // 如果是周日或者是月末最后一天，结束当前周
    if (week.length === 7 || day === daysInMonth) {
      // 如果最后一行不足7天，补充空白
      while (week.length < 7) {
        week.push({
          day: null,
          count: 0,
          amount: 0,
          percentage: 0,
          date: null
        });
      }

      result.push(week);
      week = [];
    }
  }
  // console.log('Computed: calendarData updated for', year, month + 1);
  return result;
});

// 更新日历热力图 - 主要用于日志或强制刷新后的回调
function updateCalendarLogging() {
  // calendarData.value is now computed, so it updates automatically.
  // This function can be used for logging or post-update actions if needed.
  console.log('日历数据已通过计算属性更新:', calendarData.value);

  // 强制重新渲染相关的DOM部分，如果Vue的自动更新在某些边缘情况下不够
  nextTick(() => {
    // 检查是否有数据
    if (calendarData.value && calendarData.value.length > 0) {
      // 找出有销售额的日期，确认颜色是否正确应用
      let hasColoredDays = false;
      calendarData.value.forEach(week => {
        week.forEach(day => {
          if (day.percentage > 0) {
            hasColoredDays = true;
            // console.log(`日期 ${day.date} 的销售额占比: ${day.percentage}%, 颜色: ${getColorByPercentage(day.percentage)}`);
          }
        });
      });

      if (!hasColoredDays) {
        console.warn('热力图日志: 没有找到有销售额的日期');
      } else {
        // console.log('热力图日志: 至少有一个带颜色的日期');
      }
    } else {
      console.warn('热力图日志: 日历数据为空');
    }
  });
}

// 打开月份选择器
function openMonthPicker() {
  showMonthPicker.value = true;

  // 添加点击外部关闭选择器的事件
  setTimeout(() => {
    document.addEventListener('click', closeMonthPicker);
  }, 0);
}

// 关闭月份选择器
function closeMonthPicker() {
  showMonthPicker.value = false;
  document.removeEventListener('click', closeMonthPicker);
}

// 切换年份
function changeYear(offset) {
  selectedYear.value += offset;
}

// 选择月份
function selectMonth(month) {
  selectedMonth.value = month;
  closeMonthPicker();
  // updateCalendar(); // MODIFIED - calendarData is now computed, will update automatically
}

// 切换月份 - 不再使用，保留以备将来需要
// ... existing code ...

// 根据百分比获取颜色
function getColorByPercentage(percentage) {
  // 将白色到绿色平均分为100份
  // 白色 #FFFFFF 到绿色 #00A651
  const r = Math.round(255 - (percentage * 2.55));
  const g = Math.round(255 - (percentage * 0.5));
  const b = Math.round(255 - (percentage * 2.55));

  return `rgb(${r}, ${g}, ${b})`;
}

// 选择日期
async function selectDate(date) {
  if (!date) return;

  // 切换到单日模式并选择该日期
  dateMode.value = 'single';
  selectedDate.value = date;

  // 加载该日期的数据
  await loadHistoryData();

  // 滚动到销售记录部分
  const historyRecordsElement = document.querySelector('.history-records');
  if (historyRecordsElement) {
    historyRecordsElement.scrollIntoView({ behavior: 'smooth' });
  }
}

// 打开时间段销售详情弹窗
function openTimeSlotDetails(timeSlot) {
  // 设置选中的时间段
  selectedTimeSlot.value = timeSlot;

  // 筛选该时间段的所有销售记录
  if (dateMode.value === 'single') {
    // 单日模式：筛选特定小时的记录
    timeSlotRecords.value = filteredRecords.value.filter(record => {
      if (!record.time) return false;

      // 提取小时部分
      const hour = record.time.split(':')[0];
      return hour === timeSlot;
    });

    // 按时间排序
    timeSlotRecords.value.sort((a, b) => {
      if (a.time && b.time) {
        return a.time.localeCompare(b.time);
      }
      return 0;
    });
  } else {
    // 时间段模式：筛选特定日期的记录
    timeSlotRecords.value = filteredRecords.value.filter(record =>
      record.date === timeSlot
    );

    // 按时间排序
    timeSlotRecords.value.sort((a, b) => {
      if (a.time && b.time) {
        return a.time.localeCompare(b.time);
      }
      return 0;
    });
  }

  // 显示弹窗
  showTimeDetails.value = true;
}

// 关闭时间段销售详情弹窗
function closeTimeSlotDetails() {
  showTimeDetails.value = false;
  selectedTimeSlot.value = null;
  timeSlotRecords.value = [];
}

// 使用从 productService 导入的 getProductName 函数

// 导出完整页面为图片（包含销售流水表格、当日总结和数据分析图表）
async function exportFullPageImage() {
  // 显示加载提示
  const loadingMessage = document.createElement('div');
  loadingMessage.className = 'loading-message';
  loadingMessage.innerHTML = `
    <div class="spinner"></div>
    <p class="loading-text">正在生成图片，请稍候...</p>
    <p class="loading-subtext">图片生成后将自动下载</p>
  `;
  document.body.appendChild(loadingMessage);

  try {
    // 等待更长时间，确保图表完全渲染
    await new Promise(resolve => setTimeout(resolve, 3000)); // 增加等待时间

    // 创建一个临时容器来包含所有元素
    const container = document.createElement('div');
    container.style.backgroundColor = 'white';
    container.style.padding = '30px';
    container.style.width = '1600px'; // 增加宽度，提供更多空间
    container.style.maxWidth = '100%';
    container.style.fontFamily = 'Arial, sans-serif';
    container.style.boxSizing = 'border-box';

    // 添加标题
    const title = document.createElement('h2');
    title.textContent = `${dateRangeTitle.value} 财务查询报告`;
    title.style.textAlign = 'center';
    title.style.marginBottom = '20px';
    title.style.color = '#333';
    title.style.fontSize = '28px'; // 增大字体
    title.style.fontWeight = '600';

    // 添加日期信息
    const dateInfo = document.createElement('p');
    dateInfo.textContent = `导出日期: ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`;
    dateInfo.style.textAlign = 'center';
    dateInfo.style.color = '#666';
    dateInfo.style.marginBottom = '30px';

    // 获取表格、总结和图表元素
    const tableElement = document.getElementById('history-finance-table');
    const summaryElement = document.querySelector('.history-summary');
    const chartsElement = document.querySelector('.charts-container');

    if (!tableElement) {
      throw new Error('无法找到销售流水表格元素');
    }

    // 克隆元素到临时容器
    const tableClone = tableElement.cloneNode(true);
    tableClone.style.width = '100%';
    tableClone.style.borderCollapse = 'collapse';
    tableClone.style.marginBottom = '30px';

    // 添加表格标题
    const tableTitle = document.createElement('h3');
    tableTitle.textContent = '销售流水记录';
    tableTitle.style.marginTop = '30px';
    tableTitle.style.marginBottom = '15px';
    tableTitle.style.color = '#333';

    // 添加到临时容器
    container.appendChild(title);
    container.appendChild(dateInfo);
    container.appendChild(tableTitle);
    container.appendChild(tableClone);

    // 如果有当日总结，添加当日总结
    if (summaryElement) {
      const summaryClone = summaryElement.cloneNode(true);
      summaryClone.style.marginBottom = '30px';
      container.appendChild(summaryClone);
    }

    // 如果有图表，添加图表
    if (chartsElement) {
      // 创建图表容器
      const chartsTitle = document.createElement('h3');
      chartsTitle.textContent = '数据分析图表';
      chartsTitle.style.marginTop = '30px';
      chartsTitle.style.marginBottom = '15px';
      chartsTitle.style.color = '#333';
      container.appendChild(chartsTitle);

      // 为每个图表创建单独的图片
      const chartImages = [];

      // 确保图表实例存在并且已经渲染完成
      console.log('开始获取图表图像...');

      // 获取时间与销售额分析图表
      if (salesChartInstance) {
        try {
          console.log('获取销售分布分析图...');
          const salesChartImage = salesChartInstance.getDataURL({
            type: 'png',
            pixelRatio: 3, // 提高分辨率
            backgroundColor: '#fff',
            excludeComponents: ['toolbox'] // 排除工具箱，使图表更干净
          });

          if (salesChartImage && salesChartImage.length > 0) {
            console.log('销售分布分析图获取成功');
            chartImages.push({
              title: '销售分布分析图',
              image: salesChartImage
            });
          } else {
            console.error('销售分布分析图图像为空');
          }
        } catch (error) {
          console.error('获取销售分布分析图失败:', error);
        }
      } else {
        console.warn('销售分布分析图实例不存在');
      }



      // 获取利润与返利分析图表
      if (profitChartInstance) {
        try {
          console.log('获取利润分析图...');
          const profitChartImage = profitChartInstance.getDataURL({
            type: 'png',
            pixelRatio: 3, // 提高分辨率
            backgroundColor: '#fff',
            excludeComponents: ['toolbox'] // 排除工具箱，使图表更干净
          });

          if (profitChartImage && profitChartImage.length > 0) {
            console.log('利润分析图获取成功');
            chartImages.push({
              title: '利润分析图',
              image: profitChartImage
            });
          } else {
            console.error('利润分析图图像为空');
          }
        } catch (error) {
          console.error('获取利润分析图失败:', error);
        }
      } else {
        console.warn('利润分析图实例不存在');
      }

      console.log(`成功获取 ${chartImages.length} 个图表图像`);

      // 创建图表网格
      const chartsGrid = document.createElement('div');
      chartsGrid.style.display = 'flex';
      chartsGrid.style.flexDirection = 'column';
      chartsGrid.style.gap = '50px'; // 增加间距
      chartsGrid.style.marginBottom = '50px';
      chartsGrid.style.width = '100%';

      // 确保有图表数据
      if (chartImages.length > 0) {
        console.log(`添加 ${chartImages.length} 个图表到导出图片`);

        // 添加图表到网格
        chartImages.forEach((chart, index) => {
          console.log(`处理图表 ${index + 1}: ${chart.title}`);

          const chartBox = document.createElement('div');
          chartBox.style.backgroundColor = 'white';
          chartBox.style.borderRadius = '8px';
          chartBox.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
          chartBox.style.padding = '30px';
          chartBox.style.width = '100%';
          chartBox.style.marginBottom = '20px';
          chartBox.style.boxSizing = 'border-box';

          const chartTitle = document.createElement('h4');
          chartTitle.textContent = chart.title;
          chartTitle.style.marginTop = '0';
          chartTitle.style.marginBottom = '15px';
          chartTitle.style.textAlign = 'center';
          chartTitle.style.borderBottom = '1px solid #eee';
          chartTitle.style.paddingBottom = '10px';
          chartTitle.style.color = '#333';
          chartTitle.style.fontSize = '16px';

          // 确保图表图片有效
          if (chart.image && chart.image.startsWith('data:image/')) {
            const chartImg = document.createElement('img');
            chartImg.src = chart.image;
            chartImg.style.width = '100%';
            chartImg.style.height = '500px'; // 固定高度，确保图表不会变扁
            chartImg.style.objectFit = 'contain'; // 保持图表原始比例
            chartImg.style.display = 'block';
            chartImg.style.marginTop = '20px';
            chartImg.style.border = '1px solid #eee';
            chartImg.style.borderRadius = '4px';
            chartImg.style.backgroundColor = '#f9f9f9';

            // 确保图片加载完成
            chartImg.onload = function() {
              console.log(`图表 ${index + 1} 图片加载成功`);
            };

            chartImg.onerror = function() {
              console.error(`图表 ${index + 1} 图片加载失败`);
              // 添加错误提示
              const errorText = document.createElement('p');
              errorText.textContent = '图表加载失败';
              errorText.style.color = 'red';
              errorText.style.textAlign = 'center';
              chartBox.appendChild(errorText);
            };

            chartBox.appendChild(chartTitle);
            chartBox.appendChild(chartImg);
            chartsGrid.appendChild(chartBox);
          } else {
            console.error(`图表 ${index + 1} 的图片数据无效`);
          }
        });

        container.appendChild(chartsGrid);
      } else {
        console.warn('没有图表数据可以添加到导出图片');
      }
    }

    // 添加页脚
    const footer = document.createElement('div');
    footer.style.marginTop = '30px';
    footer.style.borderTop = '1px solid #eee';
    footer.style.paddingTop = '15px';
    footer.style.textAlign = 'center';
    footer.style.color = '#999';
    footer.style.fontSize = '12px';
    footer.textContent = '© ' + new Date().getFullYear() + ' 财务查询系统';
    container.appendChild(footer);

    // 临时添加到文档以便渲染
    document.body.appendChild(container);
    container.style.position = 'absolute';
    container.style.left = '-9999px';

    // 移除表格中的编辑返利按钮
    const editRebateButtons = container.querySelectorAll('.edit-rebate-btn');
    editRebateButtons.forEach(button => {
        button.remove();
    });

    // 等待所有图片加载完成
    const allImages = container.querySelectorAll('img');
    if (allImages.length > 0) {
      console.log(`等待 ${allImages.length} 张图片加载完成...`);
      await Promise.all(
        Array.from(allImages).map(
          img => img.complete ? Promise.resolve() : new Promise(resolve => {
            img.onload = resolve;
            img.onerror = resolve; // 即使加载失败也继续
          })
        )
      );
      console.log('所有图片加载完成');
    }

    // 再次等待以确保DOM完全渲染
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 渲染为图片
    console.log('开始生成图片...');
    const canvas = await html2canvas(container, {
      useCORS: true,
      backgroundColor: 'white',
      scale: 2.5, // 提高分辨率，使图片更清晰
      logging: true, // 开启日志以便调试
      allowTaint: true, // 允许跨域图片
      foreignObjectRendering: false, // 禁用foreignObject渲染，提高兼容性
      width: container.offsetWidth,
      height: container.offsetHeight,
      imageTimeout: 10000, // 增加超时时间到10秒
      onclone: (clonedDoc) => {
        console.log('克隆DOM完成，准备渲染');
        // 确保克隆的文档中的图片都已加载
        const images = clonedDoc.querySelectorAll('img');
        console.log(`克隆文档中有 ${images.length} 张图片`);

        // 为每个图表容器添加明确的高度
        const chartContainers = clonedDoc.querySelectorAll('[style*="chart"]');
        chartContainers.forEach((el, i) => {
          el.style.minHeight = '500px'; // 增加图表容器的最小高度
          el.style.height = '500px'; // 设置固定高度
          console.log(`设置图表容器 ${i+1} 的高度为500px`);
        });

        // 确保图表图片有正确的尺寸
        const chartImages = clonedDoc.querySelectorAll('img');
        chartImages.forEach((img, i) => {
          img.style.maxWidth = '100%';
          img.style.height = '500px';
          img.style.objectFit = 'contain';
          console.log(`调整图表图片 ${i+1} 的尺寸`);
        });
      }
    });

    // 导出图片
    const image = canvas.toDataURL('image/png', 1.0); // 使用最高质量
    const link = document.createElement('a');
    link.href = image;

    // 根据查询模式设置文件名
    let fileName;
    if (dateMode.value === 'single') {
      fileName = `${selectedDate.value}-财务查询报告.png`;
    } else {
      fileName = `${startDate.value}至${endDate.value}-财务查询报告.png`;
    }

    link.download = fileName;
    link.click();
  } catch (error) {
    console.error('导出图片失败:', error);
    alert(`导出图片失败: ${error.message}\n请查看控制台获取更多信息。`);
  } finally {
    console.log('清理临时元素...');

    // 清理临时元素
    try {
      // 查找所有可能的临时容器
      const tempContainers = document.querySelectorAll('body > div');
      tempContainers.forEach(el => {
        if (el !== loadingMessage &&
            (el.style.left === '-9999px' || el.style.position === 'absolute')) {
          console.log('移除临时容器');
          document.body.removeChild(el);
        }
      });
    } catch (cleanupError) {
      console.warn('清理临时元素时出错:', cleanupError);
    }

    // 移除加载提示
    try {
      document.body.removeChild(loadingMessage);
      console.log('移除加载提示');
    } catch (loadingError) {
      console.warn('移除加载提示时出错:', loadingError);
    }

    console.log('导出过程完成');
  }
}

// 导出到Excel
function exportHistoryToExcel() {
  // 准备数据
  const data = filteredRecords.value.map(record => ({
    '日期': record.date,
    '时间': record.time || '-',
    '商品': getProductName(record.productId),
    '售货员': record.salesperson || '无',
    '金额': record.saleAmount.toFixed(2),
    '返利': calculateRebate(record).toFixed(2),
    '利润': calculateProfit(record).toFixed(2),
    '备注': record.notes || '-'
  }));

  // 创建工作簿
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.json_to_sheet(data);

  // 添加工作表
  let sheetName, fileName;

  if (dateMode.value === 'single') {
    sheetName = `销售记录-${selectedDate.value}`;
    fileName = `销售记录-${selectedDate.value}.xlsx`;
  } else {
    sheetName = `销售记录-${startDate.value}至${endDate.value}`;
    fileName = `销售记录-${startDate.value}至${endDate.value}.xlsx`;
  }

  XLSX.utils.book_append_sheet(wb, ws, sheetName);

  // 导出
  XLSX.writeFile(wb, fileName);
}

// 备份所有数据
async function backupAllData() {
  try {
    // 显示加载状态
    showFeedback('正在备份数据...', 'info');

    // 调用备份函数
    const result = await backupData();

    if (result.success) {
      showFeedback(`数据备份成功！文件名：${result.filename}`, 'success');
    } else {
      showFeedback(`备份失败：${result.error}`, 'error');
    }
  } catch (error) {
    console.error('备份数据时发生错误:', error);
    showFeedback(`备份失败：${error.message}`, 'error');
  }
}

// 图表引用
const salesChart = ref(null);
const profitChart = ref(null);

// 图表实例
let salesChartInstance = null;
let profitChartInstance = null;

// 初始化图表
function initCharts() {
  // 确保DOM已经渲染
  nextTick(() => {
    // 如果没有数据，清除图表实例并返回
    if (noDataForDate.value) {
      console.log('没有数据，清除图表实例');

      // 销毁现有图表实例
      if (salesChartInstance) {
        salesChartInstance.dispose();
        salesChartInstance = null;
      }

      if (profitChartInstance) {
        profitChartInstance.dispose();
        profitChartInstance = null;
      }

      return;
    }

    console.log('开始初始化图表...');

    // 初始化时间与销售额分析图表
    if (salesChart.value) {
      // 如果已经存在实例，先销毁
      if (salesChartInstance) {
        salesChartInstance.dispose();
      }

      console.log('初始化销售分布分析图');
      salesChartInstance = echarts.init(salesChart.value);
      renderSalesChart();
    }

    // 初始化利润与返利分析图表
    if (profitChart.value) {
      // 如果已经存在实例，先销毁
      if (profitChartInstance) {
        profitChartInstance.dispose();
      }

      console.log('初始化利润分析图');
      profitChartInstance = echarts.init(profitChart.value);
      renderProfitChart();
    }

    console.log('图表初始化完成');
  });
}

// 时间与销售额分析图表
function renderSalesChart() {
  if (!salesChartInstance) return;

  let option;

  if (dateMode.value === 'single') {
    // 单日模式：按小时区间聚合数据
    const hourlyData = {};

    // 初始化24小时的时间段
    for (let i = 0; i < 24; i++) {
      const hourStr = i.toString().padStart(2, '0');
      hourlyData[hourStr] = {
        sales: 0,
        quantity: 0
      };
    }

    // 聚合数据到小时区间
    filteredRecords.value.forEach(record => {
      if (!record.time) return;

      // 提取小时部分
      const hour = record.time.split(':')[0];

      // 累加销售额和数量
      if (hourlyData[hour]) {
        hourlyData[hour].sales += record.saleAmount;
        hourlyData[hour].quantity += 1; // 每条记录代表一个商品
      }
    });

    // 准备图表数据
    const timeData = Object.keys(hourlyData).sort(); // 按小时排序
    const salesData = timeData.map(hour => hourlyData[hour].sales);
    const quantityData = timeData.map(hour => hourlyData[hour].quantity);

    option = {
      title: {
        text: '',  // 移除标题，使用外部 h4 标题
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const hour = params[0].name;
          let result = `${hour}:00 - ${hour}:59<br/>`;

          params.forEach(param => {
            if (param.seriesName === '销售额') {
              result += `${param.seriesName}: ¥${param.value.toFixed(2)}<br/>`;
            } else {
              result += `${param.seriesName}: ${param.value} 件<br/>`;
            }
          });

          return result;
        },
        axisPointer: {
          type: 'shadow',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: ['销售额', '销售数量'],
        bottom: 0
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: {}
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        data: timeData,
        axisLabel: {
          formatter: function(value) {
            return `${value}:00`; // 显示小时
          },
          interval: 0,
          rotate: timeData.length > 12 ? 45 : 0 // 如果时间段多，旋转标签
        },
        name: ''
      },
      yAxis: [
        {
          type: 'value',
          name: '销售额 (¥)',
          position: 'left',
          axisLabel: {
            formatter: '¥{value}'
          },
          axisLine: {
            lineStyle: {
              color: '#2196F3'
            }
          }
        },
        {
          type: 'value',
          name: '销售数量 (件)',
          position: 'right',
          axisLabel: {
            formatter: '{value} 件'
          },
          axisLine: {
            lineStyle: {
              color: '#4CAF50'
            }
          }
        }
      ],
      series: [
        {
          name: '销售额',
          type: 'bar',
          data: salesData,
          itemStyle: {
            color: '#2196F3',
            borderRadius: [4, 4, 0, 0]
          },
          barWidth: '40%',
          markPoint: {
            data: [
              { type: 'max', name: '最高销售额' }
            ]
          },
          markLine: {
            data: [
              { type: 'average', name: '平均销售额' }
            ]
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              if (params.value > 0) {
                return '¥' + params.value.toFixed(0);
              }
              return '';
            }
          }
        },
        {
          name: '销售数量',
          type: 'line',
          yAxisIndex: 1,
          data: quantityData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#4CAF50'
          },
          lineStyle: {
            width: 3,
            type: 'solid'
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              if (params.value > 0) {
                return params.value + '件';
              }
              return '';
            }
          }
        }
      ]
    };
  } else {
    // 时间段模式：按日期聚合数据
    const dateStats = dailySummary.value.dateStats || {};

    // 获取日期范围内的所有日期
    const dateRange = [];
    const startDateObj = new Date(startDate.value);
    const endDateObj = new Date(endDate.value);

    for (let d = new Date(startDateObj); d <= endDateObj; d.setDate(d.getDate() + 1)) {
      dateRange.push(d.toISOString().slice(0, 10));
    }

    // 保存为组件变量，供点击事件使用
    window.chartDateRange = dateRange;

    // 准备图表数据
    const salesData = [];
    const countData = [];

    dateRange.forEach(date => {
      const stats = dateStats[date] || { sales: 0, count: 0 };
      salesData.push(stats.sales);
      countData.push(stats.count);
    });

    // 格式化日期显示
    const formattedDates = dateRange.map(date => {
      const d = new Date(date);
      return `${d.getMonth() + 1}/${d.getDate()}`;
    });

    option = {
      title: {
        text: '',  // 移除标题，使用外部 h4 标题
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const dateIndex = params[0].dataIndex;
          const fullDate = dateRange[dateIndex];
          let result = `${fullDate}<br/>`;

          params.forEach(param => {
            if (param.seriesName === '销售额') {
              result += `${param.seriesName}: ¥${param.value.toFixed(2)}<br/>`;
            } else {
              result += `${param.seriesName}: ${param.value} 件<br/>`;
            }
          });

          return result;
        },
        axisPointer: {
          type: 'shadow',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: ['销售额', '销售数量'],
        bottom: 0
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: {}
        }
      },
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: dateRange.length > 10 ? 50 : 100 // 如果日期超过10天，默认只显示前50%
        },
        {
          start: 0,
          end: dateRange.length > 10 ? 50 : 100
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: true,
        data: formattedDates,
        axisLabel: {
          interval: 0,
          rotate: formattedDates.length > 7 ? 45 : 0 // 如果日期多，旋转标签
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '销售额 (¥)',
          position: 'left',
          axisLabel: {
            formatter: '¥{value}'
          },
          axisLine: {
            lineStyle: {
              color: '#2196F3'
            }
          }
        },
        {
          type: 'value',
          name: '销售数量 (件)',
          position: 'right',
          axisLabel: {
            formatter: '{value} 件'
          },
          axisLine: {
            lineStyle: {
              color: '#4CAF50'
            }
          }
        }
      ],
      series: [
        {
          name: '销售额',
          type: 'bar',
          data: salesData,
          itemStyle: {
            color: '#2196F3',
            borderRadius: [4, 4, 0, 0]
          },
          barWidth: '40%',
          markPoint: {
            data: [
              { type: 'max', name: '最高销售额' }
            ]
          },
          markLine: {
            data: [
              { type: 'average', name: '平均销售额' }
            ]
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              if (params.value > 0) {
                return '¥' + params.value.toFixed(0);
              }
              return '';
            }
          }
        },
        {
          name: '销售数量',
          type: 'line',
          yAxisIndex: 1,
          data: countData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#4CAF50'
          },
          lineStyle: {
            width: 3,
            type: 'solid'
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              if (params.value > 0) {
                return params.value + '件';
              }
              return '';
            }
          }
        }
      ]
    };
  }

  salesChartInstance.setOption(option);

  // 添加点击事件处理
  salesChartInstance.off('click'); // 先移除之前的点击事件
  salesChartInstance.on('click', function(params) {
    if (params.componentType === 'series') {
      const timeSlot = params.name; // 获取点击的时间段

      if (dateMode.value === 'single') {
        // 单日模式：时间段是小时
        openTimeSlotDetails(timeSlot);
      } else {
        // 时间段模式：时间段是日期
        // 需要将短日期格式（如 "5/6"）转换为完整日期格式（如 "2025-05-06"）
        const dateIndex = params.dataIndex;
        const fullDate = window.chartDateRange[dateIndex]; // 使用之前保存的完整日期
        openTimeSlotDetails(fullDate);
      }
    }
  });
}





// 利润与返利分析图表
function renderProfitChart() {
  if (!profitChartInstance) return;

  // 计算总销售额、总返利和总利润
  let totalSales = 0;
  let totalRebate = 0;
  let totalProfit = 0;

  filteredRecords.value.forEach(record => {
    totalSales += record.saleAmount;
    totalRebate += calculateRebate(record);
    totalProfit += calculateProfit(record);
  });

  // 准备饼图数据
  const pieData = [
    { name: '返利', value: totalRebate },
    { name: '利润', value: totalProfit }
  ];

  // 准备柱状图数据
  const barData = [
    { name: '销售额', value: totalSales },
    { name: '返利', value: totalRebate },
    { name: '利润', value: totalProfit }
  ];

  const option = {
    title: {
      text: '',  // 移除标题，使用外部 h4 标题
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        if (params.seriesName === '占比') {
          return `${params.name}<br/>金额: ¥${params.value.toFixed(2)}<br/>占比: ${params.percent}%`;
        } else {
          return `${params.name}<br/>金额: ¥${params.value.toFixed(2)}`;
        }
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['返利', '利润', '销售额']
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    grid: {
      right: '55%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: barData.map(item => item.name),
      axisLabel: {
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '金额 (¥)',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    series: [
      {
        name: '金额',
        type: 'bar',
        data: barData.map(item => item.value),
        itemStyle: {
          color: function(params) {
            const colors = {
              '销售额': '#2196F3',
              '返利': '#FF5722',
              '利润': '#4CAF50'
            };
            return colors[params.name];
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: '¥{c}'
        }
      },
      {
        name: '占比',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['75%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: pieData,
        color: ['#FF5722', '#4CAF50']
      }
    ]
  };

  profitChartInstance.setOption(option);
}

// 显示反馈消息
function showFeedback(message, type = 'info', duration = 3000) {
  feedbackMessage.value = message;
  feedbackType.value = type;
  clearTimeout(feedbackTimeout);
  if (duration > 0) {
    feedbackTimeout = setTimeout(() => {
      feedbackMessage.value = '';
    }, duration);
  }
}

// 打开编辑弹窗
function openEditModal(record) {
  // 创建一个记录的副本进行编辑，避免直接修改原始数据
  editingRecord.value = { ...record };
  console.log("Opening edit modal for:", editingRecord.value);
}

// 关闭编辑弹窗
function closeEditModal() {
  editingRecord.value = null;
}

// 保存编辑
function handleSaveEdit(updatedData) {
  if (!editingRecord.value) return;

  console.log("Saving edit:", updatedData);
  // 调用 service 更新数据
  const result = updateFinancialRecord(editingRecord.value.id, updatedData);

  if (result.success) {
    showFeedback('记录更新成功！', 'success');
    closeEditModal();
    // 重新加载数据以反映更改
    loadHistoryData();
  } else {
    showFeedback(`更新失败: ${result.message}`, 'error');
    // 可选：保持弹窗打开让用户重试
  }
}

// 删除记录
function deleteRecord(recordId) {
  if (confirm("确定要删除这条记录吗？此操作不可撤销！")) {
    const result = deleteFinancialRecord(recordId);

    if (result.success) {
      showFeedback('记录已成功删除！', 'success');
      // 重新加载数据以反映更改
      loadHistoryData();
    } else {
      showFeedback(`删除失败: ${result.message}`, 'error');
    }
  }
}

// 更新返利比例
async function updateRebateRateSetting() {
  // 将百分比转换为小数
  const rate = parseFloat(rebateRatePercent.value) / 100;

  // 验证输入
  if (isNaN(rate) || rate < 0 || rate > 1) {
    alert('请输入有效的返利比例（0-100）');
    // 重置为当前值
    rebateRatePercent.value = Math.round(getRebateRate(selectedDate.value) * 100);
    return;
  }

  // 更新选定日期的返利比例设置
  const success = await updateRebateRate(rate, selectedDate.value);
  if (success) {
    alert(`${selectedDate.value} 的返利比例已更新为 ${rebateRatePercent.value}%`);

    // 关闭返利系数编辑器弹窗
    showRebateEditor.value = false;

    // 重新加载数据，更新返利计算
    loadHistoryData();
  } else {
    alert('更新返利比例失败');
    // 重置为当前值
    rebateRatePercent.value = Math.round(getRebateRate(selectedDate.value) * 100);
  }
}

// 监听窗口大小变化，调整图表大小
function handleResize() {
  salesChartInstance?.resize();
  profitChartInstance?.resize();
}

// 监听日期变化，重新加载图表
watch(selectedDate, async () => {
  await loadHistoryData();
});

// 监听数据变化，重新渲染图表
watch(filteredRecords, () => {
  initCharts();
}, { deep: true });

// 监听返利系数编辑器弹窗的显示状态，当弹窗显示时自动聚焦输入框
watch(showRebateEditor, (newValue) => {
  if (newValue) {
    // 更新输入框的值为当前返利比例
    rebateRatePercent.value = Math.round(getRebateRate(selectedDate.value) * 100);
    // 下一个 tick 后聚焦输入框
    nextTick(() => {
      const rebateInput = document.querySelector('.rebate-editor-modal input');
      if (rebateInput) {
        rebateInput.focus();
        rebateInput.select(); // 选中所有文本，方便直接输入
      }
    });
  }
});

// 日期更新定时器
let dateUpdateInterval = null;

// 初始加载
onMounted(async () => {
  // 加载当前日期的数据
  await loadHistoryData();

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);

  // 设置定时器，每分钟检查一次日期是否变化
  dateUpdateInterval = setInterval(() => {
    updateCurrentDate();
  }, 60000); // 60000毫秒 = 1分钟

  // 初始调用一次，确保日期是最新的
  updateCurrentDate();

  // calendarData is now computed, it will update when its dependencies (financialRecords, selectedYear, selectedMonth) change.
  // The initial rendering of calendarData will happen automatically.
  // We can watch it for logging purposes if needed.
  watch(calendarData, (newData) => {
    if (newData && newData.length > 0) {
      console.log('监听到 calendarData 更新 (onMounted):', newData.length, '周数据');
      updateCalendarLogging(); // Call logging function
    } else {
      console.warn('监听到 calendarData 为空或未生成 (onMounted)');
    }
  }, { immediate: true }); // immediate: true to log initial state and subsequent changes.

  console.log('History组件已挂载，日期更新定时器已启动');

  // 添加日期选择器的键盘事件监听，按回车键时自动关闭
  const dateInput = document.getElementById('history-date');
  if (dateInput) {
    dateInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        dateInput.blur();
        loadHistoryData();
      }
    });
  }

  // 添加日期范围选择器的键盘事件监听
  const startDateInput = document.getElementById('start-date');
  const endDateInput = document.getElementById('end-date');

  if (startDateInput) {
    startDateInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        startDateInput.blur();
        handleDateRangeChange();
      }
    });
  }

  if (endDateInput) {
    endDateInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        endDateInput.blur();
        handleDateRangeChange();
      }
    });
  }

  // 初始化售货员标签颜色缓存
  // 预先处理所有记录中的售货员，确保颜色一致性
  const allSalespersons = new Set();
  financialRecords.value.forEach(record => {
    if (record.salesperson) {
      allSalespersons.add(record.salesperson);
    }
  });

  // 为每个售货员分配颜色
  allSalespersons.forEach(name => {
    getTagType(name);
  });
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  document.removeEventListener('click', closeMonthPicker);
  salesChartInstance?.dispose();
  profitChartInstance?.dispose();

  // 清除日期更新定时器
  if (dateUpdateInterval) {
    clearInterval(dateUpdateInterval);
    dateUpdateInterval = null;
    console.log('History组件已卸载，日期更新定时器已清除');
  }
});
</script>

<style scoped>
.history-container {
  width: 100%;
  margin: 0 auto;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 0;
  box-shadow: none;
  box-sizing: border-box;
  overflow-x: hidden;
}

h2 {
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.date-selector {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.date-mode-selector {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  position: relative;
  overflow: hidden; /* 确保内容不会溢出 */
}

.mode-label {
  font-weight: 500;
  color: #333;
}

.mode-options {
  display: flex;
  gap: 20px;
}

.mode-option {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.mode-option input[type="radio"] {
  cursor: pointer;
}

.single-date-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.date-range-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
}

.date-range-input {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-selector input {
  padding: 10px 15px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 16px;
  transition: all 0.3s;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  cursor: pointer;
  background-color: white;
}

.date-selector input:hover {
  border-color: #2196F3;
  box-shadow: 0 2px 5px rgba(33, 150, 243, 0.2);
}

.date-selector input:focus {
  border-color: #2196F3;
  outline: none;
  box-shadow: 0 1px 5px rgba(33, 150, 243, 0.3);
}

/* 自定义日期选择器样式 */
input[type="date"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
  border-radius: 4px;
  margin-left: 5px;
  opacity: 0.6;
  filter: invert(0.8);
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

.no-data-message {
  text-align: center;
  padding: 30px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
}

.history-records, .history-summary {
  margin-top: 30px;
}

h3 {
  margin-bottom: 15px;
  color: #333;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

th, td {
  border: 1px solid #ddd;
  padding: 10px;
  text-align: left;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}

tfoot {
  font-weight: bold;
}

tfoot td {
  background-color: #f9f9f9;
}

.total-label {
  text-align: right;
}

.total-value {
  text-align: left;
  color: #e53935;
}

/* 售货员标签样式 */
.el-tag {
  margin-right: 5px;
  font-size: 12px;
}

.notes-cell {
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: default;
}

.actions-cell {
  white-space: nowrap;
}

.edit-button {
  background-color: #ffc107; /* Yellow */
  color: #333;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.8em;
}

.edit-button:hover {
  background-color: #e0a800;
}

.delete-button {
  background-color: #dc3545; /* Red */
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.8em;
  margin-left: 5px;
}

.delete-button:hover {
  background-color: #c82333;
}

/* 反馈消息样式 */
.feedback {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 15px;
  border-radius: 4px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  max-width: 300px;
}

.feedback.info {
  background-color: #cce5ff;
  color: #004085;
  border: 1px solid #b8daff;
}

.feedback.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.feedback.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.export-buttons {
  margin-top: 30px;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.export-btn {
  padding: 10px 20px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.export-btn:hover {
  background-color: #0b7dda;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.export-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(0,0,0,0.1);
}

.primary-btn {
  background-color: #4CAF50;
}

.primary-btn:hover {
  background-color: #45a049;
}

.backup-btn {
  background-color: #FF9800;
}

.backup-btn:hover {
  background-color: #F57C00;
}

.export-icon {
  font-size: 16px;
}

/* 加载提示样式 */
/* 返利表头样式 */
.rebate-header {
  position: relative;
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
}

.edit-rebate-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 12px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  transition: all 0.2s;
}

.edit-rebate-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

.edit-icon {
  font-size: 10px;
}

/* 返利系数编辑器弹窗样式 */
.rebate-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.rebate-editor-content {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.rebate-editor-content h3 {
  margin-top: 0;
  margin-bottom: 10px;
  text-align: center;
  color: #333;
}

.rebate-date-info {
  text-align: center;
  color: #666;
  margin-bottom: 20px;
  font-size: 14px;
}

.rebate-editor-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.modal-input-group {
  width: 100%;
  position: relative;
}

.modal-input-group input {
  width: 100%;
  padding: 10px 30px 10px 10px;
  font-size: 16px;
  text-align: center;
}

.percent-sign {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 14px;
}

.rebate-editor-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 10px;
}

.save-rebate-btn, .cancel-rebate-btn {
  flex: 1;
  padding: 8px 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.save-rebate-btn {
  background-color: #4caf50;
  color: white;
}

.save-rebate-btn:hover {
  background-color: #45a049;
}

.cancel-rebate-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cancel-rebate-btn:hover {
  background-color: #e0e0e0;
}

.loading-message {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  color: white;
}

.loading-text {
  font-size: 20px;
  font-weight: 500;
  margin: 20px 0 5px 0;
}

.loading-subtext {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin: 5px 0 0 0;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 6px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #4CAF50;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 当日总结样式 */
.history-summary {
  margin-top: 30px;
  padding: 25px;
  border-radius: 12px;
  background-color: #ffffff;
  box-shadow: 0 4px 15px rgba(0,0,0,0.08);
  transition: transform 0.3s, box-shadow 0.3s;
}

.history-summary:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.12);
}

.summary-header {
  margin-bottom: 20px;
}

.summary-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.summary-title h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.summary-icon {
  font-size: 24px;
}

.summary-content {
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
}

.summary-main {
  flex: 1;
  min-width: 280px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0,0,0,0.05);
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.summary-stat {
  display: flex;
  flex-direction: column;
  padding: 12px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  transition: transform 0.2s;
}

.summary-stat:hover {
  transform: translateY(-3px);
}

.stat-label {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 22px;
  font-weight: 600;
}

.stat-value.revenue {
  color: #2196F3;
}

.stat-value.profit {
  color: #4CAF50;
}

.stat-value.champion {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.champion-name {
  font-size: 18px;
  color: #FF9800;
}

.champion-amount {
  font-size: 16px;
  color: #FF9800;
  opacity: 0.8;
}

.summary-details {
  flex: 2;
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
}

.summary-section {
  flex: 1;
  min-width: 280px;
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0,0,0,0.05);
  transition: transform 0.3s;
}

.summary-section:hover {
  transform: translateY(-5px);
}

.summary-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #2c3e50;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-icon {
  font-size: 18px;
}

.product-list, .rebate-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.product-item, .rebate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.product-item:hover, .rebate-item:hover {
  background-color: #e9ecef;
}

.product-name {
  font-weight: 500;
  color: #495057;
  flex: 1;
}

.product-stats {
  display: flex;
  gap: 15px;
}

.product-quantity {
  color: #6c757d;
  font-size: 14px;
  white-space: nowrap;
}

.product-amount {
  color: #2196F3;
  font-weight: 500;
  white-space: nowrap;
}

.salesperson-name {
  flex: 1;
}

.rebate-amount {
  color: #FF5722;
  font-weight: 500;
}

/* 图表样式 */
.charts-container {
  margin-top: 30px;
}

.charts-grid {
  display: flex;
  flex-direction: column;
  gap: 30px;
  margin-top: 20px;
  width: 100%;
}

.chart-box {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0,0,0,0.1);
  padding: 20px;
  transition: transform 0.3s, box-shadow 0.3s;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.chart-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

.chart-box h4 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.chart {
  height: 400px; /* 适当减小高度，使多个图表在一个页面上更好地显示 */
  width: 100%;
  min-height: 350px; /* 确保最小高度 */
  max-width: 100%;
}

/* 热力图样式 */
.date-mode-selector {
  position: relative;
  overflow: visible;
}

.date-mode-flex-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: nowrap;
}

.mode-selector-row {
  flex: 1;
  min-width: 200px;
  padding-right: 170px; /* 为热力图留出空间 */
}

.heatmap-container {
  position: absolute;
  top: 5px;
  right: 15px; /* 确保不会贴边 */
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  padding: 3px;
  width: 150px; /* 进一步减小宽度 */
  z-index: 1; /* 确保在其他元素之上 */
}

.heatmap-header {
  margin-bottom: 3px;
}

.month-title {
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  position: relative;
  color: #333;
  cursor: pointer;
  padding: 2px 0;
}

.month-title:hover {
  color: #1890ff;
}

.month-picker {
  position: absolute;
  top: 20px;
  right: 0;
  width: 160px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.15);
  padding: 6px;
  z-index: 10;
}

.year-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  padding-bottom: 4px;
  border-bottom: 1px solid #eee;
}

.year-nav-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 10px;
  padding: 2px 4px;
}

.year-nav-btn:hover {
  color: #1890ff;
}

.months-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
}

.month-item {
  text-align: center;
  padding: 4px 0;
  border-radius: 2px;
  cursor: pointer;
  font-size: 10px;
  color: #666;
}

.month-item:hover {
  background-color: #f5f5f5;
}

.month-item.active {
  background-color: #1890ff;
  color: white;
}



.calendar-heatmap {
  margin-bottom: 0;
}

.weekday-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1px;
  width: 100%;
}

.weekday {
  width: 16px;
  text-align: center;
  font-size: 6px;
  color: #999;
  padding: 0;
}

.calendar-grid {
  display: flex;
  flex-direction: column;
  gap: 0;
  width: 100%;
}

.calendar-week {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1px;
  width: 100%;
}

.calendar-day {
  width: 16px;
  height: 16px;
  border: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: background-color 0.1s;
  box-sizing: border-box;
}

.calendar-day:hover {
  border-color: #ccc;
  z-index: 1;
}

.empty-day {
  background-color: transparent;
  border-color: transparent;
  cursor: default;
}

.empty-day:hover {
  border-color: transparent;
}

.day-number {
  font-size: 6px;
  font-weight: 500;
  color: #333;
  line-height: 1;
}

.has-sales {
  border-color: rgba(0,0,0,0.05);
}

.has-sales .day-number {
  color: rgba(0,0,0,0.7);
}

/* 销售额较高的日期文字颜色 */
.calendar-day[style*="rgb(0, 2"] .day-number,
.calendar-day[style*="rgb(0, 1"] .day-number {
  color: white;
}

/* 商品详情弹窗样式 */
.product-details-modal, .time-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s;
}

.product-details-content, .time-details-content {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s;
}

.product-details-header, .time-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.product-details-header h3, .time-details-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #6c757d;
  cursor: pointer;
  transition: color 0.2s;
}

.close-button:hover {
  color: #dc3545;
}

.product-details-summary, .time-details-summary {
  display: flex;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  gap: 20px;
}

.detail-stat {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.product-details-records, .time-details-records {
  padding: 20px;
  overflow-y: auto;
  max-height: 60vh;
}

.product-records-table, .time-records-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.product-records-table th,
.product-records-table td,
.time-records-table th,
.time-records-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.product-records-table th,
.time-records-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.product-records-table tbody tr:hover,
.time-records-table tbody tr:hover {
  background-color: #f8f9fa;
}

.product-records-table tfoot,
.time-records-table tfoot {
  font-weight: 600;
}

.product-records-table tfoot .total-label,
.time-records-table tfoot .total-label {
  text-align: right;
}

/* 商品项可点击样式 */
.product-item {
  cursor: pointer;
  position: relative;
}

.product-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.details-icon {
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.2s;
}

.product-item:hover .details-icon {
  opacity: 1;
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .history-container {
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  }

  .date-mode-selector {
    flex-direction: row;
    align-items: center;
  }

  .date-range-selector {
    flex-direction: row;
  }

  table {
    display: table;
    width: 100%;
  }

  .charts-grid {
    display: flex;
    flex-direction: column;
    gap: 30px;
    width: 100%;
  }
}

@media (min-width: 1200px) {
  .history-container {
    max-width: 1200px;
    padding: 20px;
  }
}

/* 移动设备样式 */
@media (max-width: 767px) {
  .history-container {
    padding: 10px;
  }

  .date-mode-selector {
    flex-direction: column;
    align-items: flex-start;
  }

  .date-mode-flex-container {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .mode-selector-row {
    width: 100%;
    margin-bottom: 10px;
  }

  .heatmap-container {
    align-self: center;
    margin-top: 15px;
  }

  .date-range-selector {
    flex-direction: column;
    width: 100%;
  }

  .date-range-input {
    width: 100%;
  }

  .date-selector input {
    width: 100%;
  }

  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .charts-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .chart-box {
    width: 100%;
    overflow-x: auto;
  }

  .export-buttons {
    flex-direction: column;
  }
}
</style>
