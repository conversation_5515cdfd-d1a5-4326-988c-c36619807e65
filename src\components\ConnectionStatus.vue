<template>
  <div class="connection-status" :class="{ connected: connected, disconnected: !connected }">
    <span class="status-dot"></span>
    <span class="status-text">{{ statusText }}</span>
    <span v-if="lastUpdate" class="last-update">最后更新: {{ formattedLastUpdate }}</span>
    <button v-if="!connected" @click="reconnect" class="reconnect-btn">重新连接</button>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useSocket } from '../services/socketService';

const { connected, lastUpdate } = useSocket();

// 重新连接函数
function reconnect() {
  // WebSocket 会自动重连，这里可以刷新页面或手动触发重连
  window.location.reload();
}

const statusText = computed(() => {
  return connected.value ? '已连接' : '未连接';
});

const formattedLastUpdate = computed(() => {
  if (!lastUpdate.value) return '';

  const date = new Date(lastUpdate.value);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  return `${hours}:${minutes}:${seconds}`;
});
</script>

<style scoped>
.connection-status {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-left: 10px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.connected {
  background-color: rgba(0, 128, 0, 0.1);
  color: #006400;
}

.connected .status-dot {
  background-color: #00c853;
  box-shadow: 0 0 5px #00c853;
}

.disconnected {
  background-color: rgba(255, 0, 0, 0.1);
  color: #8b0000;
}

.disconnected .status-dot {
  background-color: #ff3d00;
  box-shadow: 0 0 5px #ff3d00;
}

.last-update {
  margin-left: 8px;
  opacity: 0.8;
  font-size: 10px;
}

.reconnect-btn {
  margin-left: 8px;
  padding: 2px 6px;
  font-size: 10px;
  background-color: #ff3d00;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.reconnect-btn:hover {
  background-color: #dd2c00;
}
</style>
