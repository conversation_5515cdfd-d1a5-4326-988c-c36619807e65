import { ref, watch, computed } from 'vue';
import { useProducts } from './productService';
import { useServerStorage } from './serverStorage';
import { useSocket } from './socketService';
import { useSettings } from './settingsService';
import { pageVisibilityService } from './visibilityService';

const FINANCE_STORAGE_KEY = 'vue_finance_data';
const SALESPERSON_STORAGE_KEY = 'vue_salespersons';

// --- 响应式数据 --- //
const financialRecords = ref([]);
const salespersons = ref([]);

// 数据加载状态
const isLoading = ref(false);
const lastLoadTime = ref(Date.now());

// 获取商品服务的功能
const { sellProduct: decreaseProductStock, increaseStock, getProductName } = useProducts();

// 获取服务器存储服务
const { getData, setData } = useServerStorage();

// 获取 WebSocket 服务
const { onDataUpdate, requestLatestData, connected: socketConnected, lastUpdate: socketLastUpdate } = useSocket();

// 获取设置服务
const { getRebateRate } = useSettings();

// 智能刷新回调
function handlePageVisibilityChange(hiddenDuration) {
    console.log(`页面重新可见，隐藏时长: ${hiddenDuration}ms`);

    // 禁用自动刷新功能，避免数据冲突
    console.log('已禁用自动刷新以避免数据冲突，现在使用 WebSocket 实时同步');

    // 注释掉自动刷新逻辑
    // const timeSinceLastLoad = Date.now() - lastLoadTime.value;
    // if (hiddenDuration > 60000 && timeSinceLastLoad > 30000) {
    //     console.log('触发智能数据刷新');
    //     loadFinancialDataSilently();
    // }
}

// 注册页面可见性回调
pageVisibilityService.addVisibilityCallback(handlePageVisibilityChange);

// 注册实时数据更新
let unsubscribeFinance = null;
let unsubscribeSalespersons = null;

// watch 监听器控制
let watchStopFunction = null;
let isWatchersPaused = false;

// 暂停 watch 监听器
function pauseWatchers() {
    if (watchStopFunction && !isWatchersPaused) {
        watchStopFunction();
        isWatchersPaused = true;
    }
}

// 恢复 watch 监听器
function resumeWatchers() {
    if (isWatchersPaused) {
        watchStopFunction = watch([financialRecords, salespersons], debouncedSave, { deep: true });
        isWatchersPaused = false;
    }
}

// --- 辅助函数 ---
// 使用从 productService 导入的 getProductName 函数

// 处理财务记录数据更新
let lastUpdateTimestamp = 0;
const MIN_UPDATE_INTERVAL = 50; // 最小更新间隔50ms

function handleFinanceDataUpdate(data) {
    if (!data) {
        console.log("💰 [Finance] No data received for finance update");
        return;
    }

    // 防止频繁更新 - 如果距离上次更新不到50ms，跳过
    const now = Date.now();
    if (now - lastUpdateTimestamp < MIN_UPDATE_INTERVAL) {
        console.log(`💰 [Finance] Skipping update - too frequent (${now - lastUpdateTimestamp}ms < ${MIN_UPDATE_INTERVAL}ms)`);
        return;
    }
    lastUpdateTimestamp = now;

    try {
        const parsedData = typeof data === 'string' ? JSON.parse(data) : data;

        // 确保数据是数组
        if (Array.isArray(parsedData)) {
            // 如果正在保存中，跳过更新以避免冲突
            if (isSaving.value) {
                console.log("💰 [Finance] Skipping update while saving to avoid conflicts");
                return;
            }

            // 检查数据是否真的有变化
            const currentDataString = JSON.stringify(financialRecords.value);
            const newDataString = JSON.stringify(parsedData.map(r => ({ ...r, time: r.time || '', notes: r.notes || null })));

            if (currentDataString === newDataString) {
                console.log("💰 [Finance] Data unchanged, skipping update");
                return;
            }

            console.log(`💰 [Finance] Updating financial records: ${financialRecords.value.length} -> ${parsedData.length} records`);

            // 智能合并：保留本地未保存的新记录
            const serverData = parsedData.map(r => ({ ...r, time: r.time || '', notes: r.notes || null }));
            const currentData = financialRecords.value;

            // 找出本地新增的记录（ID大于最后保存时间的记录）
            const lastSaveTimestamp = lastSaveTime.value ? lastSaveTime.value.getTime() : 0;
            const localNewRecords = currentData.filter(record =>
                record.id > lastSaveTimestamp &&
                !serverData.find(serverRecord => serverRecord.id === record.id)
            );

            console.log(`💰 [Finance] Found ${localNewRecords.length} local new records to preserve`);

            // 暂停 watch 监听器，避免在数据加载时触发保存
            pauseWatchers();

            // 合并数据：服务器数据 + 本地新记录
            const mergedData = [...serverData, ...localNewRecords];
            financialRecords.value = mergedData;

            // 恢复 watch 监听器
            resumeWatchers();

            console.log("✅ [Finance] Financial records updated successfully with smart merge");
        } else {
            console.warn("⚠️ [Finance] Invalid finance data format received:", parsedData);
        }
    } catch (error) {
        console.error("❌ [Finance] Failed to process finance data update:", error);
    }
}

// 处理售货员列表数据更新
function handleSalespersonsDataUpdate(data) {
    if (!data) {
        return;
    }

    try {
        const parsedData = typeof data === 'string' ? JSON.parse(data) : data;

        // 确保数据是数组
        if (Array.isArray(parsedData)) {
            // 暂停 watch 监听器，避免在数据加载时触发保存
            pauseWatchers();

            const loadedPersons = [...parsedData];
            if (!loadedPersons.includes('无')) {
                loadedPersons.unshift('无'); // 添加"无"作为选项
            }
            salespersons.value = loadedPersons;

            // 恢复 watch 监听器
            resumeWatchers();
        } else {
            console.warn("Invalid salespersons data format received:", parsedData);
        }
    } catch (error) {
        console.error("Failed to process salespersons data update:", error);
    }
}

// --- 数据加载 --- //
async function loadFinancialData() {
    isLoading.value = true;
    try {
        await loadFinancialDataCore();
        lastLoadTime.value = Date.now();
    } finally {
        isLoading.value = false;
    }
}

// 静默加载（不显示加载状态）
async function loadFinancialDataSilently() {
    try {
        await loadFinancialDataCore();
        lastLoadTime.value = Date.now();
        console.log('数据静默刷新完成');
    } catch (error) {
        console.error('静默刷新失败:', error);
    }
}

// 核心数据加载逻辑
async function loadFinancialDataCore() {
    try {
        // 从服务器加载财务记录
        const recordsData = await getData(FINANCE_STORAGE_KEY);
        // 从服务器加载售货员列表
        const personsData = await getData(SALESPERSON_STORAGE_KEY);

        // 处理财务记录
        if (recordsData) {
            handleFinanceDataUpdate(recordsData);
        } else {
            console.log("No finance data found on server, initializing empty list.");
            financialRecords.value = [];
        }

        // 处理售货员列表
        if (personsData) {
            handleSalespersonsDataUpdate(personsData);
        } else {
            console.log("No salespersons data found on server, initializing default list.");
            salespersons.value = ['无'];
        }

        // 注册实时数据更新
        if (unsubscribeFinance) {
            unsubscribeFinance(); // 取消之前的订阅
        }
        if (unsubscribeSalespersons) {
            unsubscribeSalespersons(); // 取消之前的订阅
        }

        unsubscribeFinance = onDataUpdate(FINANCE_STORAGE_KEY, handleFinanceDataUpdate);
        unsubscribeSalespersons = onDataUpdate(SALESPERSON_STORAGE_KEY, handleSalespersonsDataUpdate);

        // 请求最新数据
        requestLatestData(FINANCE_STORAGE_KEY);
        requestLatestData(SALESPERSON_STORAGE_KEY);

    } catch (e) {
        console.error("Failed to load finance data from server:", e);

        // 尝试从本地存储加载（作为备份）
        try {
            const localRecordsData = localStorage.getItem(FINANCE_STORAGE_KEY);
            const localPersonsData = localStorage.getItem(SALESPERSON_STORAGE_KEY);

            if (localRecordsData) {
                handleFinanceDataUpdate(localRecordsData);
            } else {
                financialRecords.value = [];
            }

            if (localPersonsData) {
                handleSalespersonsDataUpdate(localPersonsData);
            } else {
                salespersons.value = ['无'];
            }
        } catch (localError) {
            console.error("Failed to parse finance data from localStorage (fallback):", localError);
            financialRecords.value = [];
            salespersons.value = ['无']; // 出错时提供默认值
        }
    }
}

// --- 数据保存 --- //
async function saveFinancialData() {
    // 防止频繁保存 - 如果距离上次保存不到100ms，跳过
    const now = Date.now();
    if (now - lastSaveTimestamp < MIN_SAVE_INTERVAL) {
        console.log(`💾 [Finance] Skipping save - too frequent (${now - lastSaveTimestamp}ms < ${MIN_SAVE_INTERVAL}ms)`);
        return { success: true, message: "保存跳过（频率限制）" };
    }

    try {
        console.log("💾 [Finance] 开始保存财务数据到服务器...");
        lastSaveTimestamp = now;

        // 并行保存财务记录和售货员列表，提高性能
        const recordsData = JSON.stringify(financialRecords.value);
        const filteredSalespersons = salespersons.value.filter(p => p !== '无');
        const personsData = JSON.stringify(filteredSalespersons);

        console.log(`📊 [Finance] 保存数据大小 - 记录: ${recordsData.length} chars, 售货员: ${personsData.length} chars`);

        // 并行执行保存操作
        const [recordsResult, personsResult] = await Promise.all([
            setData(FINANCE_STORAGE_KEY, recordsData),
            setData(SALESPERSON_STORAGE_KEY, personsData)
        ]);

        if (recordsResult.success && personsResult.success) {
            console.log("✅ [Finance] 财务数据已成功保存到服务器");

            // 同时保存到本地存储作为备份
            localStorage.setItem(FINANCE_STORAGE_KEY, recordsData);
            localStorage.setItem(SALESPERSON_STORAGE_KEY, personsData);
            return { success: true, message: "数据已保存" };
        } else {
            console.warn("⚠️ [Finance] 服务器保存部分失败，使用本地存储备份");
            console.warn("Records result:", recordsResult);
            console.warn("Persons result:", personsResult);

            // 保存到本地存储作为备份
            localStorage.setItem(FINANCE_STORAGE_KEY, recordsData);
            localStorage.setItem(SALESPERSON_STORAGE_KEY, personsData);
            console.log("📁 [Finance] 财务数据已保存到本地存储");
            return { success: false, message: "服务器保存失败，已保存到本地" };
        }
    } catch (e) {
        console.error("❌ 保存财务数据到服务器时发生错误:", e);

        // 尝试保存到本地存储作为备份
        try {
            localStorage.setItem(FINANCE_STORAGE_KEY, JSON.stringify(financialRecords.value));
            localStorage.setItem(SALESPERSON_STORAGE_KEY, JSON.stringify(salespersons.value.filter(p => p !== '无')));
            console.log("📁 财务数据已保存到本地存储（备份）");
            return { success: false, message: "服务器保存失败，已保存到本地" };
        } catch (localError) {
            console.error("❌ 保存到本地存储也失败:", localError);
            return { success: false, message: "保存失败，请检查存储空间" };
        }
    }
}

// --- 数据操作 --- //
async function addFinancialRecord(record) {
    // 基本验证
    if (!record.date || !record.time || !record.productId || isNaN(parseFloat(record.saleAmount))) {
        console.error("Invalid financial record data:", record);
        return { success: false, message: "记录数据无效" };
    }

    // 1. 尝试减少商品库存
    const decreaseResult = await decreaseProductStock(record.productId);

    // 如果减少库存失败 (例如商品不存在或已售罄)，则不添加财务记录
    if (!decreaseResult.success) {
        // 可以提供更具体的错误信息，比如区分是找不到商品还是已售罄
        let message = decreaseResult.message || "无法减少商品库存，添加销售记录失败";
        if (message.includes('售罄')) {
             message = `商品已售罄，无法添加销售记录。`;
        }
        console.error("Failed to decrease stock:", decreaseResult.message);
        return { success: false, message: message };
    }

    // 2. 库存减少成功，添加财务记录
    const newRecord = {
        id: Date.now(),
        date: record.date,
        time: record.time,
        productId: record.productId,
        salesperson: record.salesperson === '无' ? null : record.salesperson,
        saleAmount: parseFloat(record.saleAmount),
        notes: record.notes || null, // 添加 notes 字段，默认为 null
    };
    financialRecords.value.push(newRecord);

    // 立即保存新记录
    console.log("💾 [Finance] Triggering immediate save for new record");
    saveImmediately();

    // 返回成功信息，可以包含库存减少的提示（如果需要）
    return { success: true, record: newRecord, stockMessage: decreaseResult.message };
}

function addSalesperson(name) {
    const trimmedName = name.trim();
    if (trimmedName && !salespersons.value.includes(trimmedName)) {
        salespersons.value.push(trimmedName);
        // 注意：保存操作由 watch 触发
        return { success: true };
    } else if (!trimmedName) {
        return { success: false, message: "售货员名称不能为空" };
    } else {
        return { success: false, message: "售货员已存在" };
    }
}

// --- 新增：删除售货员 --- //
function deleteSalesperson(name) {
    const index = salespersons.value.findIndex(p => p === name);
    if (index !== -1 && name !== '无') { // 不允许删除"无"
        // 同时需要考虑是否需要处理 financialRecords 中关联了这个售货员的记录？
        // 暂时只移除售货员本身
        const deletedPerson = salespersons.value.splice(index, 1)[0];
        // 保存操作由 watch 触发
        console.log(`Salesperson '${deletedPerson}' deleted.`);
        return { success: true };
    } else if (name === '无') {
        return { success: false, message: '不能删除默认选项' };
    } else {
        return { success: false, message: '未找到要删除的售货员' };
    }
}
// --- 结束：删除售货员 --- //

// --- 新增：重置财务记录 --- //
function resetFinance() {
    financialRecords.value = [];
    // 售货员列表是否重置可选，这里暂时不清空售货员
    // salespersons.value = ['无'];
    // 保存操作由 watch 触发
    console.log("[FinanceService] All financial records reset.");
    return { success: true, message: "所有销售流水已被清空！" };
}
// --- 结束：重置财务记录 --- //

// --- 新增：更新财务记录 ---
async function updateFinancialRecord(recordId, updatedData) {
    const index = financialRecords.value.findIndex(r => r.id === recordId);
    if (index === -1) {
        console.error(`Record with ID ${recordId} not found for update.`);
        return { success: false, message: "未找到要更新的记录" };
    }

    const { date, salesperson, saleAmount, productId: newProductId, time, notes } = updatedData;
    const originalRecord = { ...financialRecords.value[index] };
    const oldProductId = originalRecord.productId;

    // 验证日期格式
    if (date && !/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        return { success: false, message: "无效的日期格式，请使用 YYYY-MM-DD 格式" };
    }

    // 验证售出金额
    const amountNum = parseFloat(saleAmount);
    if (isNaN(amountNum) || amountNum < 0) {
        return { success: false, message: "无效的售出金额" };
    }

    // 验证售货员是否存在 (允许为 null)
    if (salesperson && !salespersons.value.includes(salesperson)) {
        // 如果允许在编辑时添加新售货员，可以在这里调用 addSalesperson
        // 但当前设计不允许，所以返回错误
        console.warn(`Attempted to update record ${recordId} with non-existent salesperson: ${salesperson}`);
        return { success: false, message: `售货员 '${salesperson}' 不存在` };
    }

    // --- 核心逻辑：如果商品改变，调整库存 --- //
    if (newProductId !== oldProductId) {
        // 1. 尝试将原商品库存 +1
        const increaseResult = await increaseStock(oldProductId, 1);
        if (!increaseResult.success) {
            console.error(`Failed to increase stock for old product ${oldProductId}:`, increaseResult.message);
            // 重要：无法恢复原商品库存，这是一个潜在的数据不一致风险。需要记录或警告。
            return { success: false, message: `无法恢复原商品 (${getProductName(oldProductId)}) 库存: ${increaseResult.message}` };
        }

        // 2. 尝试将新商品库存 -1
        const decreaseResult = await decreaseProductStock(newProductId);
        if (!decreaseResult.success) {
            console.error(`Failed to decrease stock for new product ${newProductId}:`, decreaseResult.message);
            // 尝试回滚：将原商品库存 -1 (如果增加成功了)
            const rollbackResult = await decreaseProductStock(oldProductId);
            if (!rollbackResult.success) {
                console.error(`CRITICAL: Failed to rollback stock increase for old product ${oldProductId} after failing to decrease new product stock. Manual correction needed.`);
                 // 极严重情况，数据已不一致，需要人工干预
                 return { success: false, message: `无法售出新商品 (${getProductName(newProductId)}) 且无法自动恢复原商品库存，请手动检查库存！错误: ${decreaseResult.message}` };
            }
            return { success: false, message: `无法售出新选择的商品 (${getProductName(newProductId)}): ${decreaseResult.message}` };
        }
    }
    // --- 库存调整结束 --- //

    // 验证时间格式
    if (time && !/^\d{2}:\d{2}$/.test(time)) {
        return { success: false, message: "无效的时间格式，请使用 HH:MM 格式" };
    }

    // 更新记录
    // 使用一个新的对象来确保响应性
    financialRecords.value[index] = {
        ...originalRecord,
        date: date || originalRecord.date, // 更新日期字段
        productId: newProductId,
        salesperson: salesperson === '无' ? null : salesperson,
        saleAmount: amountNum,
        time: time || originalRecord.time, // 更新时间字段
        notes: notes || null, // 更新 notes 字段
    };

    // 注意：保存操作由 watch 监听器自动触发，无需手动调用
    console.log(`Financial record ${recordId} updated successfully.`);
    return { success: true };
}
// --- 新增：删除财务记录 ---
async function deleteFinancialRecord(recordId) {
    const index = financialRecords.value.findIndex(r => r.id === recordId);
    if (index === -1) {
        console.error(`Record with ID ${recordId} not found for deletion.`);
        return { success: false, message: "未找到要删除的记录" };
    }

    const recordToDelete = financialRecords.value[index];
    const productId = recordToDelete.productId;

    // 尝试将商品库存 +1
    if (productId) { // 确保有商品 ID
        const increaseResult = await increaseStock(productId, 1);
        if (!increaseResult.success) {
            // 库存增加失败，可能商品已被删除或达到上限
            // 显示警告，但继续删除记录
            console.warn(`删除记录 ${recordId} 时恢复商品 ${productId} 库存失败: ${increaseResult.message}`);
            // 可以选择是否继续删除记录
            // return { success: false, message: `删除记录失败：无法恢复商品库存 - ${increaseResult.message}` };
        }
    } else {
        console.warn(`记录 ${recordId} 没有关联的商品ID，无法恢复库存。`);
    }

    // 从财务记录中移除
    financialRecords.value.splice(index, 1);

    // 注意：保存操作由 watch 监听器自动触发，无需手动调用
    console.log(`Financial record ${recordId} deleted successfully.`);
    return { success: true };
}
// --- 结束：删除财务记录 ---

// --- 结束：更新财务记录 ---

// --- 计算属性 --- //
// 可以添加计算总销售额和总利润的 computed 属性
const totalSaleAmount = computed(() => {
    return financialRecords.value.reduce((sum, record) => sum + (record.saleAmount || 0), 0);
});

const totalProfit = computed(() => {
    return financialRecords.value.reduce((sum, record) => {
        const saleAmount = record.saleAmount || 0;
        // 使用记录日期的返利比例
        const rebateRate = getRebateRate(record.date);
        const rebate = record.salesperson ? Math.round(saleAmount * rebateRate) : 0;
        const profit = saleAmount - rebate;
        return sum + profit;
    }, 0);
});


// --- 保存状态管理 --- //
const isSaving = ref(false);
const lastSaveTime = ref(null);
const saveStatus = ref('idle'); // 'idle', 'saving', 'success', 'error'

// 防抖保存
let saveTimeout = null;
let lastSaveTimestamp = 0;
const SAVE_DEBOUNCE_DELAY = 200; // 200ms防抖延迟
const MIN_SAVE_INTERVAL = 100; // 最小保存间隔100ms

// 防抖保存函数
function debouncedSave() {
    // 防止频繁保存 - 如果距离上次保存不到100ms，跳过
    const now = Date.now();
    if (now - lastSaveTimestamp < MIN_SAVE_INTERVAL) {
        console.log(`⏱️ [Finance] Skipping save - too frequent (${now - lastSaveTimestamp}ms < ${MIN_SAVE_INTERVAL}ms)`);
        return;
    }

    // 清除之前的定时器
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }

    // 设置新的定时器
    saveTimeout = setTimeout(() => {
        // 再次检查是否应该保存
        if (Date.now() - lastSaveTimestamp >= MIN_SAVE_INTERVAL) {
            lastSaveTimestamp = Date.now();
            saveFinancialDataWithStatus();
        }
    }, SAVE_DEBOUNCE_DELAY);

    console.log(`⏱️ [Finance] Save debounced for ${SAVE_DEBOUNCE_DELAY}ms`);
}

// 立即保存函数（用于用户主动操作）
async function saveImmediately() {
    // 清除防抖定时器
    if (saveTimeout) {
        clearTimeout(saveTimeout);
        saveTimeout = null;
    }

    console.log("🚀 [Finance] Immediate save triggered");
    return await saveFinancialDataWithStatus();
}

// 带状态管理的保存函数
async function saveFinancialDataWithStatus() {
    if (isSaving.value) {
        console.log("⏳ 正在保存中，跳过重复保存请求");
        return;
    }

    isSaving.value = true;
    saveStatus.value = 'saving';

    try {
        const result = await saveFinancialData();

        if (result.success) {
            saveStatus.value = 'success';
            lastSaveTime.value = new Date();
            console.log("✅ 数据保存成功，更新最后保存时间:", lastSaveTime.value);
        } else {
            saveStatus.value = 'error';
            console.warn("⚠️ 数据保存失败:", result.message);
        }

        return result;
    } catch (error) {
        saveStatus.value = 'error';
        console.error("❌ 保存过程中发生错误:", error);
        return { success: false, message: "保存过程中发生错误" };
    } finally {
        isSaving.value = false;

        // 3秒后重置状态
        setTimeout(() => {
            if (saveStatus.value !== 'saving') {
                saveStatus.value = 'idle';
            }
        }, 3000);
    }
}

// --- 初始化和监听 --- //
loadFinancialData();

// 初始化 watch 监听器，使用防抖保存函数
watchStopFunction = watch([financialRecords, salespersons], debouncedSave, { deep: true });

// --- 导出 --- //
export const useFinance = () => {
    return {
        financialRecords,
        salespersons,
        loadFinancialData,
        saveFinancialData,
        saveFinancialDataWithStatus,
        addFinancialRecord,
        addSalesperson,
        deleteSalesperson,
        updateFinancialRecord,
        deleteFinancialRecord,
        resetFinance,
        totalSaleAmount,
        totalProfit,
        // 保存状态
        isSaving,
        lastSaveTime,
        saveStatus
    };
};